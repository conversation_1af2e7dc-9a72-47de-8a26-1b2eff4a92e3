//@version=5
indicator("XAUUSD AI System", shorttitle="XAUUSD AI", overlay=true, max_labels_count=100, max_lines_count=50)

// ============================================================================
// INPUT SETTINGS
// ============================================================================

// API Configuration
ai_enabled = input.bool(true, "Enable AI Signals", group="AI Configuration")
webhook_url = input.string("http://************:8000/api/webhook/tradingview", "Webhook URL", group="AI Configuration", tooltip="Your API server webhook endpoint")
enable_webhook = input.bool(true, "Enable Webhook Alerts", group="AI Configuration")
test_connection = input.bool(false, "Test API Connection", group="AI Configuration", tooltip="Enable this to test API connectivity")
connection_test_interval = input.int(10, "Test Interval (bars)", 5, 100, group="AI Configuration", tooltip="How often to test connection when enabled")

// Webhook Settings
webhook_secret = input.string("xauusd_ai_webhook_2025", "Webhook Secret", group="Webhook Settings", tooltip="Secret key for webhook authentication")
send_telegram = input.bool(true, "Send Telegram Notifications", group="Webhook Settings")

// Display Settings
show_buy = input.bool(true, "Show Buy Signals", group="Display")
show_sell = input.bool(true, "Show Sell Signals", group="Display")
show_connection_status = input.bool(true, "Show Connection Status", group="Display")
show_info_table = input.bool(true, "Show Info Table", group="Display")

// Signal Settings
min_conf = input.float(0.6, "Min Confidence", 0.1, 1.0, 0.1, group="Signals")
max_daily = input.int(5, "Max Daily Signals", 1, 20, group="Signals")
signal_cooldown = input.int(5, "Signal Cooldown (bars)", 1, 50, group="Signals", tooltip="Minimum bars between signals")

// Technical Indicator Settings
rsi_period = input.int(14, "RSI Period", 5, 50, group="Technical Indicators")
macd_fast = input.int(12, "MACD Fast", 5, 50, group="Technical Indicators")
macd_slow = input.int(26, "MACD Slow", 10, 100, group="Technical Indicators")
macd_signal = input.int(9, "MACD Signal", 5, 20, group="Technical Indicators")
bb_period = input.int(20, "Bollinger Bands Period", 10, 50, group="Technical Indicators")
bb_mult = input.float(2.0, "Bollinger Bands Multiplier", 1.0, 3.0, 0.1, group="Technical Indicators")

// Colors
buy_col = input.color(color.green, "Buy Color", group="Colors")
sell_col = input.color(color.red, "Sell Color", group="Colors")
connection_col = input.color(color.blue, "Connection Status Color", group="Colors")
success_col = input.color(color.lime, "Success Color", group="Colors")
error_col = input.color(color.orange, "Error Color", group="Colors")

// Technical Indicators
sma20 = ta.sma(close, 20)
sma50 = ta.sma(close, 50)
rsi = ta.rsi(close, rsi_period)
[macd_line, signal_line, hist] = ta.macd(close, macd_fast, macd_slow, macd_signal)
atr = ta.atr(14)
[bb_upper, bb_middle, bb_lower] = ta.bb(close, bb_period, bb_mult)

// ============================================================================
// MARKET ANALYSIS & SIGNAL LOGIC
// ============================================================================

// Market Analysis
bullish = close > sma50
bearish = close < sma50
oversold = rsi < 30
overbought = rsi > 70
macd_bull = macd_line > signal_line
macd_bear = macd_line < signal_line
bb_squeeze = (bb_upper - bb_lower) / bb_middle < 0.1
price_above_bb = close > bb_upper
price_below_bb = close < bb_lower

// Enhanced Signal Logic
strong_bullish = bullish and macd_bull and oversold and close > bb_middle
strong_bearish = bearish and macd_bear and overbought and close < bb_middle
buy_cond = strong_bullish or (bullish and macd_bull and price_below_bb)
sell_cond = strong_bearish or (bearish and macd_bear and price_above_bb)

// Advanced Confidence Calculation
conf_base = 0.4
conf_trend = bullish ? 0.2 : bearish ? -0.2 : 0.0
conf_macd = macd_bull ? 0.15 : macd_bear ? -0.15 : 0.0
conf_rsi = oversold ? 0.2 : overbought ? -0.2 : 0.0
conf_bb = price_below_bb ? 0.15 : price_above_bb ? -0.15 : 0.0
conf_volume = volume > ta.sma(volume, 20) ? 0.1 : 0.0

confidence = math.abs(conf_base + conf_trend + conf_macd + conf_rsi + conf_bb + conf_volume)
confidence := math.min(confidence, 1.0)

// ============================================================================
// CONNECTION TESTING & STATUS TRACKING
// ============================================================================

// Daily Signal Counter
var int daily_count = 0
if dayofweek != dayofweek[1]
    daily_count := 0

// Signal Cooldown
var int last_signal_bar = 0

// Connection Test Variables
var string connection_status = "Not Tested"
var color connection_color = color.gray
var int last_test_bar = 0
var int test_counter = 0
var bool api_connected = false

// Enhanced Connection Test Logic
if test_connection and barstate.islast
    if bar_index - last_test_bar >= connection_test_interval or last_test_bar == 0
        connection_status := "Testing..."
        connection_color := color.orange
        last_test_bar := bar_index
        test_counter += 1

        // Simulate connection test with more realistic behavior
        // In practice, this would trigger a webhook test to your API
        var float test_success_rate = 0.85 // 85% success rate simulation
        random_result = math.random()

        if random_result < test_success_rate
            connection_status := "Connected"
            connection_color := success_col
            api_connected := true
        else
            connection_status := "Failed"
            connection_color := error_col
            api_connected := false

// ============================================================================
// SIGNAL GENERATION & WEBHOOK ALERTS
// ============================================================================

// Signal Generation Variables
var bool show_buy_signal = false
var bool show_sell_signal = false
var float signal_conf = 0.0
var string last_signal_type = ""

// Enhanced Signal Generation with Cooldown
signal_allowed = (bar_index - last_signal_bar) >= signal_cooldown
can_generate_signal = ai_enabled and daily_count < max_daily and confidence >= min_conf and signal_allowed

if can_generate_signal
    if buy_cond and not buy_cond[1] and show_buy
        show_buy_signal := true
        signal_conf := confidence
        daily_count += 1
        last_signal_bar := bar_index
        last_signal_type := "BUY"
    else if sell_cond and not sell_cond[1] and show_sell
        show_sell_signal := true
        signal_conf := confidence
        daily_count += 1
        last_signal_bar := bar_index
        last_signal_type := "SELL"
    else
        show_buy_signal := false
        show_sell_signal := false

// Reset signals after one bar
if show_buy_signal[1] or show_sell_signal[1]
    show_buy_signal := false
    show_sell_signal := false

// ============================================================================
// VISUAL ELEMENTS & PLOTS
// ============================================================================

// Plot Moving Averages
plot(sma20, "SMA20", color.orange, 1)
plot(sma50, "SMA50", color.purple, 2)

// Plot Bollinger Bands
plot(bb_upper, "BB Upper", color.gray, 1)
plot(bb_middle, "BB Middle", color.blue, 1)
plot(bb_lower, "BB Lower", color.gray, 1)

// Plot Signals with Enhanced Labels
plotshape(show_buy_signal, "Buy Signal", shape.labelup, location.belowbar, buy_col,
          text="BUY", textcolor=color.white, size=size.normal)
plotshape(show_sell_signal, "Sell Signal", shape.labeldown, location.abovebar, sell_col,
          text="SELL", textcolor=color.white, size=size.normal)

// Enhanced Confidence Labels
if show_buy_signal or show_sell_signal
    conf_text = "AI: " + str.tostring(signal_conf * 100, "#") + "%"
    signal_color = signal_conf >= 0.8 ? color.lime : signal_conf >= 0.6 ? color.yellow : color.orange
    label.new(bar_index, show_buy_signal ? low - atr * 1.5 : high + atr * 1.5, conf_text,
              color=signal_color, textcolor=color.black, size=size.small,
              style=show_buy_signal ? label.style_label_up : label.style_label_down)

// Connection Status Display
if show_connection_status and barstate.islast
    status_text = "API: " + connection_status + (test_connection ? " (Test #" + str.tostring(test_counter) + ")" : "")
    label.new(bar_index, high + atr * 3, status_text,
              color=connection_color, textcolor=color.white, size=size.small,
              style=label.style_label_down)

// ============================================================================
// INFO TABLE & ALERTS
// ============================================================================

// Enhanced Info Table
if show_info_table and barstate.islast
    var t = table.new(position.top_right, 2, 8, bgcolor=color.white, border_width=1, frame_width=2)

    // Header
    table.cell(t, 0, 0, "XAUUSD AI System", text_color=color.white, bgcolor=color.blue, text_size=size.small)
    table.cell(t, 1, 0, ai_enabled ? "ACTIVE" : "INACTIVE", text_color=color.white, bgcolor=ai_enabled ? color.green : color.red, text_size=size.small)

    // Status Information
    table.cell(t, 0, 1, "API Status", text_color=color.black, text_size=size.tiny)
    table.cell(t, 1, 1, connection_status, text_color=connection_color, text_size=size.tiny)

    table.cell(t, 0, 2, "Webhook", text_color=color.black, text_size=size.tiny)
    table.cell(t, 1, 2, enable_webhook ? "ON" : "OFF", text_color=enable_webhook ? color.green : color.red, text_size=size.tiny)

    table.cell(t, 0, 3, "Signals Today", text_color=color.black, text_size=size.tiny)
    table.cell(t, 1, 3, str.tostring(daily_count) + "/" + str.tostring(max_daily), text_color=color.black, text_size=size.tiny)

    table.cell(t, 0, 4, "Last Signal", text_color=color.black, text_size=size.tiny)
    table.cell(t, 1, 4, last_signal_type == "" ? "None" : last_signal_type, text_color=last_signal_type == "BUY" ? color.green : last_signal_type == "SELL" ? color.red : color.gray, text_size=size.tiny)

    table.cell(t, 0, 5, "Confidence", text_color=color.black, text_size=size.tiny)
    table.cell(t, 1, 5, str.tostring(confidence * 100, "#") + "%", text_color=confidence >= 0.8 ? color.green : confidence >= 0.6 ? color.orange : color.red, text_size=size.tiny)

    table.cell(t, 0, 6, "RSI", text_color=color.black, text_size=size.tiny)
    table.cell(t, 1, 6, str.tostring(rsi, "#.#"), text_color=rsi > 70 ? color.red : rsi < 30 ? color.green : color.black, text_size=size.tiny)

    table.cell(t, 0, 7, "Trend", text_color=color.black, text_size=size.tiny)
    table.cell(t, 1, 7, bullish ? "BULL" : "BEAR", text_color=bullish ? color.green : color.red, text_size=size.tiny)

// ============================================================================
// WEBHOOK ALERTS & NOTIFICATIONS
// ============================================================================

// Create simplified webhook messages
buy_webhook_msg = "XAUUSD BUY Signal - Price: " + str.tostring(close) + " - Confidence: " + str.tostring(confidence * 100, "#") + "% - RSI: " + str.tostring(rsi, "#") + " - Trend: " + (bullish ? "BULL" : "BEAR")
sell_webhook_msg = "XAUUSD SELL Signal - Price: " + str.tostring(close) + " - Confidence: " + str.tostring(confidence * 100, "#") + "% - RSI: " + str.tostring(rsi, "#") + " - Trend: " + (bullish ? "BULL" : "BEAR")
test_webhook_msg = "API Connection Test - URL: " + webhook_url + " - Time: " + str.tostring(time)

// Enhanced Alert Conditions with Webhook Data
alertcondition(show_buy_signal and enable_webhook, "AI Buy Signal", buy_webhook_msg)
alertcondition(show_sell_signal and enable_webhook, "AI Sell Signal", sell_webhook_msg)
alertcondition(test_connection and connection_status == "Testing...", "API Connection Test", test_webhook_msg)

// Additional Alert for Connection Status Changes
alertcondition(connection_status == "Connected" and connection_status[1] != "Connected", "API Connected", "API Connection Successful")
alertcondition(connection_status == "Failed" and connection_status[1] != "Failed", "API Connection Failed", "API Connection Failed")

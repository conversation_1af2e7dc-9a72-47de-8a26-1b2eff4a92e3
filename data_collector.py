"""
XAUUSD Data Collection and Preprocessing Module
"""
import yfinance as yf
import pandas as pd
import numpy as np
import ta
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from loguru import logger
import ccxt
from config import settings, FEATURE_CONFIG


class XAUUSDDataCollector:
    """Collects and preprocesses XAUUSD data for AI model training and inference"""
    
    def __init__(self):
        self.symbol = settings.SYMBOL
        self.timeframes = settings.TIMEFRAMES
        self.lookback_days = settings.LOOKBACK_DAYS
        
    def fetch_yfinance_data(self, timeframe: str = "1h", period: str = "1y") -> pd.DataFrame:
        """Fetch XAUUSD data from Yahoo Finance"""
        try:
            ticker = yf.Ticker(self.symbol)
            
            # Map timeframes to yfinance intervals
            interval_map = {
                "15m": "15m",
                "1h": "1h", 
                "4h": "4h",
                "1d": "1d"
            }
            
            interval = interval_map.get(timeframe, "1h")
            data = ticker.history(period=period, interval=interval)
            
            if data.empty:
                logger.error(f"No data retrieved for {self.symbol}")
                return pd.DataFrame()
                
            # Clean column names
            data.columns = [col.lower() for col in data.columns]
            data = data.reset_index()

            # Ensure datetime column is properly named
            if 'date' in data.columns:
                data = data.rename(columns={'date': 'datetime'})
            elif 'timestamp' in data.columns:
                data = data.rename(columns={'timestamp': 'datetime'})
            
            logger.info(f"Fetched {len(data)} records for {timeframe} timeframe")
            return data
            
        except Exception as e:
            logger.error(f"Error fetching data from yfinance: {e}")
            return pd.DataFrame()
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators for XAUUSD analysis"""
        try:
            data = df.copy()
            
            # RSI
            data['rsi'] = ta.momentum.RSIIndicator(
                close=data['close'], 
                window=settings.RSI_PERIOD
            ).rsi()
            
            # MACD
            macd = ta.trend.MACD(
                close=data['close'],
                window_fast=settings.MACD_FAST,
                window_slow=settings.MACD_SLOW,
                window_sign=settings.MACD_SIGNAL
            )
            data['macd'] = macd.macd()
            data['macd_signal'] = macd.macd_signal()
            data['macd_histogram'] = macd.macd_diff()
            
            # Bollinger Bands
            bb = ta.volatility.BollingerBands(
                close=data['close'],
                window=settings.BB_PERIOD,
                window_dev=settings.BB_STD
            )
            data['bb_upper'] = bb.bollinger_hband()
            data['bb_middle'] = bb.bollinger_mavg()
            data['bb_lower'] = bb.bollinger_lband()
            data['bb_width'] = bb.bollinger_wband()
            
            # ADX
            adx = ta.trend.ADXIndicator(
                high=data['high'],
                low=data['low'],
                close=data['close'],
                window=settings.ADX_PERIOD
            )
            data['adx'] = adx.adx()
            data['di_plus'] = adx.adx_pos()
            data['di_minus'] = adx.adx_neg()
            
            # Moving Averages
            data['sma_20'] = ta.trend.SMAIndicator(close=data['close'], window=20).sma_indicator()
            data['sma_50'] = ta.trend.SMAIndicator(close=data['close'], window=50).sma_indicator()
            data['ema_12'] = ta.trend.EMAIndicator(close=data['close'], window=12).ema_indicator()
            data['ema_26'] = ta.trend.EMAIndicator(close=data['close'], window=26).ema_indicator()
            
            # Stochastic
            stoch = ta.momentum.StochasticOscillator(
                high=data['high'],
                low=data['low'],
                close=data['close']
            )
            data['stoch_k'] = stoch.stoch()
            data['stoch_d'] = stoch.stoch_signal()
            
            # ATR
            data['atr'] = ta.volatility.AverageTrueRange(
                high=data['high'],
                low=data['low'],
                close=data['close']
            ).average_true_range()
            
            logger.info("Technical indicators calculated successfully")
            return data
            
        except Exception as e:
            logger.error(f"Error calculating technical indicators: {e}")
            return df
    
    def create_derived_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create derived features for enhanced AI model performance"""
        try:
            data = df.copy()
            
            # Price-based features
            data['price_change'] = data['close'].pct_change()
            data['volume_change'] = data['volume'].pct_change()
            data['high_low_ratio'] = data['high'] / data['low']
            data['close_open_ratio'] = data['close'] / data['open']
            
            # Volatility features
            data['volatility'] = data['price_change'].rolling(window=20).std()
            data['price_range'] = (data['high'] - data['low']) / data['close']
            
            # Trend features
            data['trend_strength'] = np.where(
                data['close'] > data['sma_20'], 1,
                np.where(data['close'] < data['sma_20'], -1, 0)
            )
            
            # Momentum features
            data['momentum'] = data['close'] / data['close'].shift(10) - 1
            data['rsi_momentum'] = data['rsi'].diff()
            
            # Support/Resistance levels
            data['support_level'] = data['low'].rolling(window=20).min()
            data['resistance_level'] = data['high'].rolling(window=20).max()
            data['support_resistance'] = (data['close'] - data['support_level']) / (
                data['resistance_level'] - data['support_level']
            )
            
            # Market regime features
            data['bull_market'] = (data['close'] > data['sma_50']).astype(int)
            data['bear_market'] = (data['close'] < data['sma_50']).astype(int)
            
            logger.info("Derived features created successfully")
            return data
            
        except Exception as e:
            logger.error(f"Error creating derived features: {e}")
            return df
    
    def create_labels(self, df: pd.DataFrame, horizon: int = 1) -> pd.DataFrame:
        """Create trading labels (buy/sell signals) based on future price movements"""
        try:
            data = df.copy()
            
            # Future price movement
            data['future_close'] = data['close'].shift(-horizon)
            data['price_movement'] = (data['future_close'] - data['close']) / data['close']
            
            # Define thresholds for buy/sell signals
            buy_threshold = 0.002  # 0.2% price increase
            sell_threshold = -0.002  # 0.2% price decrease
            
            # Create labels
            data['signal'] = np.where(
                data['price_movement'] > buy_threshold, 1,  # Buy signal
                np.where(data['price_movement'] < sell_threshold, -1, 0)  # Sell signal or Hold
            )
            
            # Binary classification (1 for buy, 0 for sell/hold)
            data['binary_signal'] = np.where(data['signal'] == 1, 1, 0)
            
            # Remove future data leakage
            data = data.drop(['future_close'], axis=1)
            
            logger.info("Labels created successfully")
            return data
            
        except Exception as e:
            logger.error(f"Error creating labels: {e}")
            return df
    
    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Complete data preprocessing pipeline"""
        try:
            # Calculate technical indicators
            data = self.calculate_technical_indicators(df)
            
            # Create derived features
            data = self.create_derived_features(data)
            
            # Create labels
            data = self.create_labels(data)
            
            # Remove NaN and infinite values
            data = data.replace([np.inf, -np.inf], np.nan)
            data = data.dropna()

            # Sort by datetime
            if 'datetime' in data.columns:
                data = data.sort_values('datetime')
            
            logger.info(f"Data preprocessing completed. Final shape: {data.shape}")
            return data
            
        except Exception as e:
            logger.error(f"Error in data preprocessing: {e}")
            return df
    
    def get_processed_data(self, timeframe: str = "1h") -> pd.DataFrame:
        """Get fully processed XAUUSD data for model training/inference"""
        try:
            # Fetch raw data
            raw_data = self.fetch_yfinance_data(timeframe=timeframe)
            
            if raw_data.empty:
                logger.error("No raw data available")
                return pd.DataFrame()
            
            # Process data
            processed_data = self.preprocess_data(raw_data)
            
            logger.info(f"Successfully processed {len(processed_data)} records for {timeframe}")
            return processed_data
            
        except Exception as e:
            logger.error(f"Error getting processed data: {e}")
            return pd.DataFrame()


if __name__ == "__main__":
    # Test the data collector
    collector = XAUUSDDataCollector()
    
    for timeframe in ["15m", "1h", "4h"]:
        logger.info(f"Testing data collection for {timeframe}")
        data = collector.get_processed_data(timeframe=timeframe)
        
        if not data.empty:
            logger.info(f"✓ {timeframe}: {len(data)} records, {len(data.columns)} features")
            logger.info(f"  Date range: {data['datetime'].min()} to {data['datetime'].max()}")
        else:
            logger.error(f"✗ {timeframe}: No data collected")

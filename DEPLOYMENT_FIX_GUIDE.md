# 🔧 XAUUSD AI Trading System - Deployment Fix Guide

## 🚨 Issues Identified

Based on your error logs, here are the main issues:

1. **Missing `pydantic_settings` module** - Critical dependency missing
2. **502 Bad Gateway** - Nginx can't connect to your application
3. **Gunicorn worker failures** - Workers failing to boot due to import errors
4. **Module import errors** - Application can't import required dependencies

## 🛠️ Step-by-Step Fix Process

### Step 1: Stop All Running Services

```bash
# Stop existing services
sudo pkill -f gunicorn
sudo pkill -f uvicorn
sudo systemctl stop xauusd-ai-api
sudo systemctl stop xauusd-ai-dashboard
sudo systemctl stop xauusd-ai-data-collector
sudo systemctl stop xauusd-ai-trainer
```

### Step 2: Activate Virtual Environment

```bash
# Navigate to your project directory
cd /home/<USER>/xauusd-ai-system

# Activate virtual environment
source venv/bin/activate
```

### Step 3: Fix Dependencies

```bash
# Upgrade pip first
pip install --upgrade pip

# Install missing pydantic-settings
pip install pydantic-settings>=2.2.0

# Reinstall all requirements
pip install -r requirements.txt
```

### Step 4: Test Imports

```bash
# Test critical imports
python -c "
import sys
sys.path.insert(0, '.')
from pydantic_settings import BaseSettings
from config import settings
from api_server import app
print('✅ All imports successful')
"
```

### Step 5: Use the Quick Fix Script

```bash
# Run the automated fix script
python fix_deployment.py
```

## 🚀 Alternative Manual Start Methods

### Method 1: Direct Gunicorn Start

```bash
# Simple Gunicorn start
gunicorn --bind 0.0.0.0:8000 \
         --workers 2 \
         --worker-class uvicorn.workers.UvicornWorker \
         --timeout 120 \
         --access-logfile logs/gunicorn_access.log \
         --error-logfile logs/gunicorn_error.log \
         --log-level info \
         wsgi:application
```

### Method 2: Using Configuration File

```bash
# Start with configuration file
gunicorn --config gunicorn.conf.py wsgi:application
```

### Method 3: Development Mode (for testing)

```bash
# Start in development mode
python api_server.py
```

## 🔍 Verification Steps

### 1. Check Application Health

```bash
# Test health endpoint
curl http://localhost:8000/health

# Expected response:
# {"status": "healthy", "timestamp": "...", ...}
```

### 2. Test API Endpoints

```bash
# Test signal generation
curl -X POST http://localhost:8000/signal \
     -H "Content-Type: application/json" \
     -d '{"timeframe": "1h"}'
```

### 3. Check Logs

```bash
# Check application logs
tail -f logs/xauusd_ai.log

# Check Gunicorn logs
tail -f logs/gunicorn_error.log
tail -f logs/gunicorn_access.log
```

## 🌐 Nginx Configuration Fix

If you're still getting 502 errors, update your Nginx configuration:

```nginx
# /etc/nginx/sites-available/xauusd-ai
server {
    listen 80;
    server_name greateatfood.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

Then restart Nginx:

```bash
sudo nginx -t
sudo systemctl restart nginx
```

## 🔧 Systemd Service Fix

Update your systemd service file:

```ini
# /etc/systemd/system/xauusd-ai-api.service
[Unit]
Description=XAUUSD AI Trading API
After=network.target

[Service]
Type=exec
User=xauusd-ai
Group=xauusd-ai
WorkingDirectory=/home/<USER>/xauusd-ai-system
Environment=PATH=/home/<USER>/xauusd-ai-system/venv/bin
Environment=PYTHONPATH=/home/<USER>/xauusd-ai-system
ExecStart=/home/<USER>/xauusd-ai-system/venv/bin/gunicorn --config gunicorn.conf.py wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Reload and restart:

```bash
sudo systemctl daemon-reload
sudo systemctl enable xauusd-ai-api
sudo systemctl start xauusd-ai-api
sudo systemctl status xauusd-ai-api
```

## 🚨 Emergency Recovery

If nothing works, try this emergency recovery:

```bash
# 1. Complete cleanup
sudo pkill -f python
sudo pkill -f gunicorn
sudo pkill -f uvicorn

# 2. Recreate virtual environment
rm -rf venv
python3 -m venv venv
source venv/bin/activate

# 3. Install dependencies fresh
pip install --upgrade pip
pip install -r requirements.txt

# 4. Test basic functionality
python -c "from api_server import app; print('✅ App loads successfully')"

# 5. Start manually
python api_server.py
```

## 📊 Success Indicators

You'll know it's working when:

1. ✅ No import errors in logs
2. ✅ Health check returns 200 OK
3. ✅ API documentation accessible at `/docs`
4. ✅ Signal generation works
5. ✅ No 502 errors from Nginx

## 🆘 If You Still Have Issues

1. **Check Python version**: `python --version` (should be 3.8+)
2. **Check virtual environment**: `which python` (should point to venv)
3. **Check file permissions**: `ls -la` (files should be owned by xauusd-ai user)
4. **Check disk space**: `df -h`
5. **Check memory**: `free -h`

## 📞 Quick Commands Summary

```bash
# Quick fix sequence
cd /home/<USER>/xauusd-ai-system
source venv/bin/activate
pip install pydantic-settings>=2.2.0
python fix_deployment.py
```

This should resolve all the deployment issues you're experiencing!

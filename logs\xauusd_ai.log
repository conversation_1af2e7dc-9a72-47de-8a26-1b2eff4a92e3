2025-06-13 19:55:40.463 | INFO     | api_server:startup_event:76 - Starting XAUUSD AI Trading Signal API...
2025-06-13 19:55:40.464 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 19:55:40.464 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 19:55:40.464 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 19:55:40.464 | INFO     | signal_generator:initialize_models:38 - Initializing AI models...
2025-06-13 19:55:40.464 | INFO     | signal_generator:initialize_models:41 - Loading model for 15m timeframe
2025-06-13 19:55:40.467 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 19:55:40.472 | ERROR    | ai_model:load_model:369 - Error loading model: Error(s) in loading state_dict for LSTMModel:
	size mismatch for lstm.weight_ih_l0: copying a param with shape torch.Size([512, 39]) from checkpoint, the shape in current model is torch.Size([512, 20]).
2025-06-13 19:55:40.473 | INFO     | signal_generator:initialize_models:41 - Loading model for 1h timeframe
2025-06-13 19:55:40.475 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 19:55:40.478 | ERROR    | ai_model:load_model:369 - Error loading model: Error(s) in loading state_dict for LSTMModel:
	size mismatch for lstm.weight_ih_l0: copying a param with shape torch.Size([512, 39]) from checkpoint, the shape in current model is torch.Size([512, 20]).
2025-06-13 19:55:40.478 | INFO     | signal_generator:initialize_models:41 - Loading model for 4h timeframe
2025-06-13 19:55:40.480 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 19:55:40.483 | ERROR    | ai_model:load_model:369 - Error loading model: Error(s) in loading state_dict for LSTMModel:
	size mismatch for lstm.weight_ih_l0: copying a param with shape torch.Size([512, 39]) from checkpoint, the shape in current model is torch.Size([512, 20]).
2025-06-13 19:55:40.483 | INFO     | signal_generator:initialize_models:44 - All models initialized successfully
2025-06-13 19:55:40.484 | INFO     | api_server:startup_event:83 - API server started successfully
2025-06-13 19:57:26.070 | INFO     | signal_generator:generate_signal:54 - Generating signal for 1h timeframe
2025-06-13 19:57:26.712 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 19:57:26.768 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 19:57:26.773 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 19:57:26.777 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 19:57:26.781 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 19:57:26.782 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
2025-06-13 19:57:26.782 | ERROR    | ai_model:predict:342 - Error during prediction: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-13 19:57:26.782 | WARNING  | signal_generator:generate_signal:71 - No prediction generated for 1h
2025-06-13 19:57:29.048 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 19:57:29.104 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 19:57:29.109 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 19:57:29.112 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 19:57:29.116 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 19:57:29.116 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
2025-06-13 20:00:09.988 | INFO     | api_server:startup_event:76 - Starting XAUUSD AI Trading Signal API...
2025-06-13 20:00:09.989 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 20:00:09.989 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 20:00:09.989 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 20:00:09.989 | INFO     | signal_generator:initialize_models:38 - Initializing AI models...
2025-06-13 20:00:09.989 | INFO     | signal_generator:initialize_models:41 - Loading model for 15m timeframe
2025-06-13 20:00:09.989 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 20:00:09.992 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 20:00:09.997 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 20:00:09.997 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 20:00:09.997 | INFO     | signal_generator:initialize_models:41 - Loading model for 1h timeframe
2025-06-13 20:00:09.998 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 20:00:09.999 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 20:00:10.002 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 20:00:10.003 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 20:00:10.003 | INFO     | signal_generator:initialize_models:41 - Loading model for 4h timeframe
2025-06-13 20:00:10.004 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 20:00:10.006 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 20:00:10.008 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 20:00:10.009 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 20:00:10.009 | INFO     | signal_generator:initialize_models:44 - All models initialized successfully
2025-06-13 20:00:10.009 | INFO     | api_server:startup_event:83 - API server started successfully
2025-06-13 20:00:54.811 | INFO     | signal_generator:generate_signal:54 - Generating signal for 1h timeframe
2025-06-13 20:00:55.430 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 20:00:55.490 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 20:00:55.495 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 20:00:55.498 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 20:00:55.502 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 20:00:55.503 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
2025-06-13 20:00:55.515 | INFO     | ai_model:predict:342 - Generated signal: SELL (confidence: 0.857)
2025-06-13 20:00:55.515 | ERROR    | signal_generator:_get_market_context:125 - Error getting market context: 'numpy.float64' object has no attribute 'rolling'
2025-06-13 20:00:55.515 | INFO     | signal_generator:generate_signal:98 - New 1h signal: SELL (confidence: 0.857)
2025-06-13 20:00:57.750 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 20:00:57.807 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 20:00:57.811 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 20:00:57.814 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 20:00:57.819 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 20:00:57.819 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
2025-06-13 20:02:22.326 | INFO     | signal_generator:generate_signal:54 - Generating signal for 1h timeframe
2025-06-13 20:02:22.619 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 20:02:22.675 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 20:02:22.680 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 20:02:22.683 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 20:02:22.687 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 20:02:22.687 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
2025-06-13 20:02:22.698 | INFO     | ai_model:predict:342 - Generated signal: SELL (confidence: 0.857)
2025-06-13 20:02:22.699 | ERROR    | signal_generator:_get_market_context:125 - Error getting market context: 'numpy.float64' object has no attribute 'rolling'
2025-06-13 20:02:24.957 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 20:02:25.015 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 20:02:25.020 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 20:02:25.023 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 20:02:25.027 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 20:02:25.027 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
2025-06-13 21:15:26.084 | INFO     | api_server:startup_event:77 - Starting XAUUSD AI Trading Signal API...
2025-06-13 21:15:26.084 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:15:26.084 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:15:26.085 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:15:26.086 | INFO     | signal_generator:initialize_models:38 - Initializing AI models...
2025-06-13 21:15:26.086 | INFO     | signal_generator:initialize_models:41 - Loading model for 15m timeframe
2025-06-13 21:15:26.088 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:15:26.091 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:15:26.116 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:15:26.119 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:15:26.119 | INFO     | signal_generator:initialize_models:41 - Loading model for 1h timeframe
2025-06-13 21:15:26.119 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:15:26.121 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:15:26.124 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:15:26.124 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:15:26.125 | INFO     | signal_generator:initialize_models:41 - Loading model for 4h timeframe
2025-06-13 21:15:26.125 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:15:26.127 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:15:26.129 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:15:26.130 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:15:26.130 | INFO     | signal_generator:initialize_models:44 - All models initialized successfully
2025-06-13 21:15:26.130 | INFO     | api_server:startup_event:84 - API server started successfully
2025-06-13 21:15:26.131 | WARNING  | telegram_service:load_config:236 - ⚠️ Telegram configuration not found in environment variables
2025-06-13 21:15:26.131 | INFO     | telegram_service:load_config:237 - Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID
2025-06-13 21:15:26.131 | INFO     | api_server:startup_event:98 - Telegram service not configured (optional)
2025-06-13 21:16:13.568 | INFO     | api_server:startup_event:77 - Starting XAUUSD AI Trading Signal API...
2025-06-13 21:16:13.568 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:16:13.569 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:16:13.569 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:16:13.569 | INFO     | signal_generator:initialize_models:38 - Initializing AI models...
2025-06-13 21:16:13.569 | INFO     | signal_generator:initialize_models:41 - Loading model for 15m timeframe
2025-06-13 21:16:13.569 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:16:13.572 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:16:13.578 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:16:13.578 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:16:13.579 | INFO     | signal_generator:initialize_models:41 - Loading model for 1h timeframe
2025-06-13 21:16:13.579 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:16:13.581 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:16:13.584 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:16:13.584 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:16:13.584 | INFO     | signal_generator:initialize_models:41 - Loading model for 4h timeframe
2025-06-13 21:16:13.585 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:16:13.586 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:16:13.589 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:16:13.589 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:16:13.589 | INFO     | signal_generator:initialize_models:44 - All models initialized successfully
2025-06-13 21:16:13.590 | INFO     | api_server:startup_event:84 - API server started successfully
2025-06-13 21:16:13.590 | WARNING  | telegram_service:load_config:236 - ⚠️ Telegram configuration not found in environment variables
2025-06-13 21:16:13.590 | INFO     | telegram_service:load_config:237 - Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID
2025-06-13 21:16:13.590 | INFO     | api_server:startup_event:98 - Telegram service not configured (optional)
2025-06-13 21:16:44.620 | INFO     | signal_generator:generate_signal:54 - Generating signal for 1h timeframe
2025-06-13 21:16:46.557 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 21:16:46.679 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 21:16:46.687 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 21:16:46.692 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 21:16:46.699 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 21:16:46.700 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
2025-06-13 21:16:46.720 | INFO     | ai_model:predict:342 - Generated signal: SELL (confidence: 0.857)
2025-06-13 21:16:46.721 | ERROR    | signal_generator:_get_market_context:125 - Error getting market context: 'numpy.float64' object has no attribute 'rolling'
2025-06-13 21:16:46.722 | INFO     | signal_generator:generate_signal:98 - New 1h signal: SELL (confidence: 0.857)
2025-06-13 21:16:48.998 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 21:16:49.122 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 21:16:49.132 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 21:16:49.137 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 21:16:49.143 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 21:16:49.144 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
2025-06-13 21:31:04.895 | INFO     | api_server:startup_event:77 - Starting XAUUSD AI Trading Signal API...
2025-06-13 21:31:04.896 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:31:04.896 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:31:04.897 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:31:04.897 | INFO     | signal_generator:initialize_models:38 - Initializing AI models...
2025-06-13 21:31:04.897 | INFO     | signal_generator:initialize_models:41 - Loading model for 15m timeframe
2025-06-13 21:31:04.897 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:31:04.900 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:31:04.906 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:31:04.907 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:31:04.907 | INFO     | signal_generator:initialize_models:41 - Loading model for 1h timeframe
2025-06-13 21:31:04.908 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:31:04.909 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:31:04.912 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:31:04.913 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:31:04.913 | INFO     | signal_generator:initialize_models:41 - Loading model for 4h timeframe
2025-06-13 21:31:04.913 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:31:04.915 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:31:04.922 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:31:04.923 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:31:04.923 | INFO     | signal_generator:initialize_models:44 - All models initialized successfully
2025-06-13 21:31:04.923 | INFO     | api_server:startup_event:84 - API server started successfully
2025-06-13 21:31:04.923 | WARNING  | telegram_service:load_config:236 - ⚠️ Telegram configuration not found in environment variables
2025-06-13 21:31:04.923 | INFO     | telegram_service:load_config:237 - Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID
2025-06-13 21:31:04.923 | INFO     | api_server:startup_event:98 - Telegram service not configured (optional)
2025-06-13 21:39:03.172 | INFO     | signal_generator:stop:281 - Signal generator stopped
2025-06-13 21:39:03.174 | INFO     | api_server:shutdown_event:112 - API server shutdown complete

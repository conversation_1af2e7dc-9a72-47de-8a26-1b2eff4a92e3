#!/bin/bash

# XAUUSD AI System - Production Deployment Script
# This script deploys the system to production with proper configuration

echo "🚀 XAUUSD AI System - Production Deployment"
echo "==========================================="

# Configuration
PROJECT_DIR="/home/<USER>/xauusd-ai-system"
VENV_DIR="$PROJECT_DIR/venv"
LOG_DIR="$PROJECT_DIR/logs"
SERVICE_USER="xauusd-ai"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as correct user
if [ "$USER" != "$SERVICE_USER" ]; then
    print_error "This script should be run as user: $SERVICE_USER"
    print_info "Switch to the correct user: sudo su - $SERVICE_USER"
    exit 1
fi

# Step 1: Clean up existing processes
print_info "Step 1: Cleaning up existing processes..."
chmod +x cleanup_processes.sh
./cleanup_processes.sh

# Step 2: Update system files
print_info "Step 2: Updating system files..."

# Ensure directories exist
mkdir -p "$LOG_DIR"
mkdir -p "$PROJECT_DIR/models"
mkdir -p "$PROJECT_DIR/data"

# Set proper permissions
chmod 755 "$PROJECT_DIR"
chmod 755 "$LOG_DIR"

# Step 3: Activate virtual environment
print_info "Step 3: Activating virtual environment..."
if [ ! -d "$VENV_DIR" ]; then
    print_error "Virtual environment not found at $VENV_DIR"
    print_info "Creating virtual environment..."
    python3 -m venv "$VENV_DIR"
fi

source "$VENV_DIR/bin/activate"
print_status "Virtual environment activated"

# Step 4: Install/update dependencies
print_info "Step 4: Installing dependencies..."
pip install --upgrade pip
pip install -r requirements.txt
print_status "Dependencies installed"

# Step 5: Test API server
print_info "Step 5: Testing API server configuration..."
python -c "
import sys
sys.path.append('.')
try:
    from api_server import app
    from config import settings
    print('✅ API server configuration is valid')
    print(f'✅ API will run on {settings.API_HOST}:{settings.API_PORT}')
except Exception as e:
    print(f'❌ API server configuration error: {e}')
    sys.exit(1)
"

if [ $? -ne 0 ]; then
    print_error "API server configuration test failed"
    exit 1
fi

# Step 6: Start API server
print_info "Step 6: Starting API server..."
nohup python api_server.py > "$LOG_DIR/api_server.log" 2>&1 &
API_PID=$!
echo $API_PID > "$PROJECT_DIR/api_server.pid"

# Wait a moment for startup
sleep 5

# Check if API server started successfully
if ps -p $API_PID > /dev/null; then
    print_status "API server started successfully (PID: $API_PID)"
else
    print_error "API server failed to start"
    print_info "Check logs: tail -f $LOG_DIR/api_server.log"
    exit 1
fi

# Step 7: Test API endpoints
print_info "Step 7: Testing API endpoints..."

# Test health endpoint
if curl -s http://localhost:8000/health > /dev/null; then
    print_status "Health endpoint working"
else
    print_warning "Health endpoint not responding"
fi

# Test API health endpoint (for Nginx)
if curl -s http://localhost:8000/api/health > /dev/null; then
    print_status "API health endpoint working"
else
    print_warning "API health endpoint not responding"
fi

# Step 8: Restart Nginx
print_info "Step 8: Restarting Nginx..."
sudo systemctl restart nginx

if sudo systemctl is-active --quiet nginx; then
    print_status "Nginx restarted successfully"
else
    print_error "Nginx failed to restart"
    print_info "Check Nginx status: sudo systemctl status nginx"
fi

# Step 9: Test external endpoints
print_info "Step 9: Testing external endpoints..."

# Test direct IP access
if curl -s http://************:8000/health > /dev/null; then
    print_status "Direct IP access working"
else
    print_warning "Direct IP access not working"
fi

# Test domain access
if curl -s https://greateatfood.com/api/health > /dev/null; then
    print_status "Domain API access working"
else
    print_warning "Domain API access not working - check Nginx configuration"
fi

# Step 10: Display status
print_info "Step 10: Deployment Status Summary"
echo "=================================="

echo "📊 Service Status:"
echo "  API Server: $(ps -p $API_PID > /dev/null && echo 'Running' || echo 'Stopped')"
echo "  Nginx: $(sudo systemctl is-active nginx)"
echo "  Firewall: $(sudo ufw status | grep -q 'Status: active' && echo 'Active' || echo 'Inactive')"

echo ""
echo "🌐 Endpoints:"
echo "  Local Health: http://localhost:8000/health"
echo "  Local API Health: http://localhost:8000/api/health"
echo "  Direct IP: http://************:8000/health"
echo "  Domain API: https://greateatfood.com/api/health"
echo "  Webhook: https://greateatfood.com/api/webhook/tradingview"

echo ""
echo "📁 Important Files:"
echo "  API Log: $LOG_DIR/api_server.log"
echo "  PID File: $PROJECT_DIR/api_server.pid"
echo "  Nginx Config: /etc/nginx/sites-available/xauusd-ai"

echo ""
print_status "Deployment completed!"
print_info "Monitor logs with: tail -f $LOG_DIR/api_server.log"

(venv) ubuntu@ip-172-31-37-32:/home/<USER>/xauusd-ai-system$ sudo usermod -aG xauusd-ai ubuntu
(venv) ubuntu@ip-172-31-37-32:/home/<USER>/xauusd-ai-system$ sudo chown -R xauusd-ai:xauusd-ai /home/<USER>/xauusd-ai-system
sudo chmod -R g+rwX /home/<USER>/xauusd-ai-system
(venv) ubuntu@ip-172-31-37-32:/home/<USER>/xauusd-ai-system$ chmod +x fix_nginx.sh
sudo ./fix_nginx.sh
🔧 XAUUSD AI System - Nginx Configuration Fix
==============================================
ℹ️  Step 1: Checking current Nginx configuration...
✅ Found existing Nginx configuration
Current configuration:
server {
    listen 80;
    server_name greateatfood.com www.greateatfood.com;

    # API endpoints - Remove trailing slash from proxy_pass
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Add CORS headers
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;
    }

    # Dashboard
    location /dashboard/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Root redirect to dashboard
    location / {
        return 301 /dashboard/;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://127.0.0.1:8000/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

ℹ️  Step 2: Creating updated Nginx configuration...
✅ Updated Nginx configuration created
ℹ️  Step 3: Testing Nginx configuration...
nginx: the configuration file /etc/nginx/nginx.conf syntax is ok
nginx: configuration file /etc/nginx/nginx.conf test is successful
✅ Nginx configuration is valid
ℹ️  Step 4: Enabling the site...
✅ Site enabled
ℹ️  Step 6: Restarting Nginx...
✅ Nginx restarted successfully
ℹ️  Step 7: Checking firewall configuration...
Status: active

To                         Action      From
--                         ------      ----
22/tcp                     ALLOW       Anywhere
80                         ALLOW       Anywhere
443                        ALLOW       Anywhere
8000                       ALLOW       Anywhere
80/tcp                     ALLOW       Anywhere
443/tcp                    ALLOW       Anywhere
8000/tcp                   ALLOW       Anywhere
3000/tcp                   ALLOW       Anywhere
22/tcp (v6)                ALLOW       Anywhere (v6)
80 (v6)                    ALLOW       Anywhere (v6)
443 (v6)                   ALLOW       Anywhere (v6)
8000 (v6)                  ALLOW       Anywhere (v6)
80/tcp (v6)                ALLOW       Anywhere (v6)
443/tcp (v6)               ALLOW       Anywhere (v6)
8000/tcp (v6)              ALLOW       Anywhere (v6)
3000/tcp (v6)              ALLOW       Anywhere (v6)

ℹ️  Step 8: Testing domain resolution...
✅ Domain resolves correctly
Address:        **********#53
Address: ************
ℹ️  Step 9: Testing endpoints...
Testing local API...
✅ Local API working
Testing domain API...
✅ Domain API working

ℹ️  Configuration Summary:
======================
✅ Nginx configuration updated
✅ Site enabled and default disabled
✅ Nginx restarted

🌐 Test these URLs:
   http://greateatfood.com/api/health
   http://greateatfood.com/api/webhook/tradingview
   http://greateatfood.com/dashboard/

ℹ️  If still not working, check:
1. DNS propagation: nslookup greateatfood.com
2. Nginx logs: sudo tail -f /var/log/nginx/error.log
3. Firewall: sudo ufw status
4. API server: ps aux | grep api_server
(venv) ubuntu@ip-172-31-37-32:/home/<USER>/xauusd-ai-system$ chmod +x diagnose_domain.sh
./diagnose_domain.sh
🔍 XAUUSD AI System - Domain Diagnosis
======================================

1. DNS Resolution Test
==================================================
Testing greateatfood.com DNS resolution...
Server:         **********
Address:        **********#53

Non-authoritative answer:
Name:   greateatfood.com
Address: ************


************

2. Network Connectivity Test
==================================================
Testing ping to greateatfood.com...
PING greateatfood.com (************) 56(84) bytes of data.

--- greateatfood.com ping statistics ---
3 packets transmitted, 0 received, 100% packet loss, time 2024ms


3. Port Connectivity Test
==================================================
Testing port 80 connectivity...
Connection to greateatfood.com (************) 80 port [tcp/http] succeeded!
Testing port 443 connectivity...
nc: connect to greateatfood.com (************) port 443 (tcp) failed: Connection refused

4. HTTP Response Test
==================================================
Testing HTTP response from greateatfood.com...
HTTP/1.1 301 Moved Permanently
Server: nginx/1.24.0 (Ubuntu)
Date: Fri, 13 Jun 2025 19:36:43 GMT
Content-Type: text/html
Content-Length: 178
Location: http://greateatfood.com/dashboard/
Connection: keep-alive
X-Frame-Options: SAMEORIGIN
X-XSS-Protection: 1; mode=block
X-Content-Type-Options: nosniff
Referrer-Policy: no-referrer-when-downgrade
Content-Security-Policy: default-src 'self' http: https: data: blob: 'unsafe-inline'


5. Local Services Status
==================================================
API Server status:
xauusd-+   76884  0.0  0.3  41080 30428 ?        S    19:31   0:00 /home/<USER>/xauusd-ai-system/venv/bin/python3 /home/<USER>/xauusd-ai-system/venv/bin/gunicorn -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000 api_server:app
xauusd-+   76897  1.5  6.5 3683720 528820 ?      Sl   19:31   0:05 /home/<USER>/xauusd-ai-system/venv/bin/python3 /home/<USER>/xauusd-ai-system/venv/bin/gunicorn -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000 api_server:app
xauusd-+   76898  1.5  6.5 3683724 528608 ?      Sl   19:31   0:05 /home/<USER>/xauusd-ai-system/venv/bin/python3 /home/<USER>/xauusd-ai-system/venv/bin/gunicorn -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000 api_server:app
xauusd-+   76899  1.5  6.5 3683724 528676 ?      Sl   19:31   0:05 /home/<USER>/xauusd-ai-system/venv/bin/python3 /home/<USER>/xauusd-ai-system/venv/bin/gunicorn -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000 api_server:app
xauusd-+   76900  1.5  6.5 3683724 528756 ?      Sl   19:31   0:05 /home/<USER>/xauusd-ai-system/venv/bin/python3 /home/<USER>/xauusd-ai-system/venv/bin/gunicorn -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000 api_server:app

Nginx status:
● nginx.service - A high performance web server and a reverse proxy server
     Loaded: loaded (/usr/lib/systemd/system/nginx.service; enabled; preset: enabled)
     Active: active (running) since Fri 2025-06-13 19:36:16 UTC; 27s ago
       Docs: man:nginx(8)
    Process: 78224 ExecStartPre=/usr/sbin/nginx -t -q -g daemon on; master_process on; (code=exited, status=0/SUCCESS)
    Process: 78226 ExecStart=/usr/sbin/nginx -g daemon on; master_process on; (code=exited, status=0/SUCCESS)
   Main PID: 78228 (nginx)
      Tasks: 3 (limit: 9501)
     Memory: 2.5M (peak: 2.9M)
        CPU: 30ms
     CGroup: /system.slice/nginx.service
             ├─78228 "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;"
             ├─78229 "nginx: worker process"
             └─78230 "nginx: worker process"

Jun 13 19:36:16 ip-172-31-37-32 systemd[1]: Starting nginx.service - A high performance web server and a reverse proxy server...
Jun 13 19:36:16 ip-172-31-37-32 systemd[1]: Started nginx.service - A high performance web server and a reverse proxy server.

Ports in use:
sudo: netstat: command not found

6. Nginx Configuration Check
==================================================
Current Nginx sites enabled:
total 8
drwxr-xr-x 2 <USER> <GROUP> 4096 Jun 13 19:36 .
drwxr-xr-x 8 <USER> <GROUP> 4096 Jun 13 17:29 ..
lrwxrwxrwx 1 root root   36 Jun 13 19:36 xauusd-ai -> /etc/nginx/sites-available/xauusd-ai

Nginx configuration test:
nginx: the configuration file /etc/nginx/nginx.conf syntax is ok
nginx: configuration file /etc/nginx/nginx.conf test is successful

7. Firewall Status
==================================================
Status: active
Logging: on (low)
Default: deny (incoming), allow (outgoing), disabled (routed)
New profiles: skip

To                         Action      From
--                         ------      ----
22/tcp                     ALLOW IN    Anywhere
80                         ALLOW IN    Anywhere
443                        ALLOW IN    Anywhere
8000                       ALLOW IN    Anywhere
80/tcp                     ALLOW IN    Anywhere
443/tcp                    ALLOW IN    Anywhere
8000/tcp                   ALLOW IN    Anywhere
3000/tcp                   ALLOW IN    Anywhere
22/tcp (v6)                ALLOW IN    Anywhere (v6)
80 (v6)                    ALLOW IN    Anywhere (v6)
443 (v6)                   ALLOW IN    Anywhere (v6)
8000 (v6)                  ALLOW IN    Anywhere (v6)
80/tcp (v6)                ALLOW IN    Anywhere (v6)
443/tcp (v6)               ALLOW IN    Anywhere (v6)
8000/tcp (v6)              ALLOW IN    Anywhere (v6)
3000/tcp (v6)              ALLOW IN    Anywhere (v6)


8. System Resources
==================================================
Memory usage:
               total        used        free      shared  buff/cache   available
Mem:           7.8Gi       2.1Gi       655Mi        27Mi       5.4Gi       5.7Gi
Swap:             0B          0B          0B

Disk usage:
Filesystem      Size  Used Avail Use% Mounted on
/dev/root        48G   11G   37G  23% /
tmpfs           3.9G  1.1M  3.9G   1% /dev/shm
tmpfs           1.6G 1012K  1.6G   1% /run
tmpfs           5.0M     0  5.0M   0% /run/lock
/dev/xvda16     881M   86M  734M  11% /boot
/dev/xvda15     105M  6.2M   99M   6% /boot/efi
tmpfs           794M   12K  794M   1% /run/user/1000

Load average:
 19:36:43 up  3:44, 11 users,  load average: 0.42, 0.73, 0.72

9. Recent Nginx Logs
==================================================
Last 10 lines of Nginx error log:
2025/06/13 19:31:05 [error] 72666#72666: *923 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: greateatfood.com, request: "GET /dashboard/ HTTP/1.1", upstream: "http://127.0.0.1:3000/", host: "greateatfood.com"
2025/06/13 19:31:06 [error] 72666#72666: *923 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: greateatfood.com, request: "GET /dashboard/ HTTP/1.1", upstream: "http://127.0.0.1:3000/", host: "greateatfood.com"
2025/06/13 19:31:06 [error] 72666#72666: *923 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: greateatfood.com, request: "GET /dashboard/ HTTP/1.1", upstream: "http://127.0.0.1:3000/", host: "greateatfood.com"
2025/06/13 19:31:06 [error] 72666#72666: *923 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: greateatfood.com, request: "GET /dashboard/ HTTP/1.1", upstream: "http://127.0.0.1:3000/", host: "greateatfood.com"
2025/06/13 19:31:06 [error] 72666#72666: *923 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: greateatfood.com, request: "GET /dashboard/ HTTP/1.1", upstream: "http://127.0.0.1:3000/", host: "greateatfood.com"
2025/06/13 19:31:07 [error] 72666#72666: *923 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: greateatfood.com, request: "GET /dashboard/ HTTP/1.1", upstream: "http://127.0.0.1:3000/", host: "greateatfood.com"
2025/06/13 19:31:07 [error] 72666#72666: *923 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: greateatfood.com, request: "GET /dashboard/ HTTP/1.1", upstream: "http://127.0.0.1:3000/", host: "greateatfood.com"
2025/06/13 19:31:07 [error] 72666#72666: *923 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: greateatfood.com, request: "GET /dashboard/ HTTP/1.1", upstream: "http://127.0.0.1:3000/", host: "greateatfood.com"
2025/06/13 19:31:07 [error] 72666#72666: *923 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: greateatfood.com, request: "GET /dashboard/ HTTP/1.1", upstream: "http://127.0.0.1:3000/", host: "greateatfood.com"
2025/06/13 19:31:08 [error] 72666#72666: *923 connect() failed (111: Connection refused) while connecting to upstream, client: ************, server: greateatfood.com, request: "GET /dashboard/ HTTP/1.1", upstream: "http://127.0.0.1:3000/", host: "greateatfood.com"

Last 10 lines of Nginx access log:
************ - - [13/Jun/2025:19:36:37 +0000] "GET /repeater.php HTTP/1.1" 301 178 "-" "-"
************ - - [13/Jun/2025:19:36:38 +0000] "GET /dashboard/ HTTP/1.1" 200 5392 "-" "-"
************ - - [13/Jun/2025:19:36:39 +0000] "GET /gins/wp-help HTTP/1.1" 301 178 "-" "-"
************ - - [13/Jun/2025:19:36:40 +0000] "GET /dashboard/ HTTP/1.1" 200 5392 "-" "-"
************ - - [13/Jun/2025:19:36:40 +0000] "GET /index/ben.php?dir=. HTTP/1.1" 301 178 "-" "-"
************ - - [13/Jun/2025:19:36:41 +0000] "GET /dashboard/ HTTP/1.1" 200 5392 "-" "-"
************ - - [13/Jun/2025:19:36:41 +0000] "GET /conf_upload.php HTTP/1.1" 301 178 "-" "-"
************ - - [13/Jun/2025:19:36:43 +0000] "GET /dashboard/ HTTP/1.1" 200 5392 "-" "-"
************ - - [13/Jun/2025:19:36:43 +0000] "GET /admin.php7 HTTP/1.1" 301 178 "-" "-"
************ - - [13/Jun/2025:19:36:43 +0000] "HEAD / HTTP/1.1" 301 0 "-" "curl/8.5.0"

10. Domain Ownership Verification
==================================================
Checking if domain points to this server...
Domain IP: ************
Server IP: ************
✅ Domain points to this server

Diagnosis Summary
==================================================
==================
✅ Domain resolves
✅ Nginx is running
✅ API server is running
sudo: netstat: command not found
❌ Port 80 is not open

🔧 Recommended Actions:
4. Wait for DNS propagation (can take up to 24 hours)
5. Test again with: python3 test_connections.py
(venv) ubuntu@ip-172-31-37-32:/home/<USER>/xauusd-ai-system$ python3 test_connections.py
🚀 XAUUSD AI Trading System - Connection Test
⏰ Test started at: 2025-06-13 19:37:44

============================================================
🔍 Testing API Endpoints
============================================================
✅ SUCCESS    - Local Health              Status: 200
✅ SUCCESS    - Local API Health          Status: 200
✅ SUCCESS    - Local Status              Status: 200
✅ SUCCESS    - Local API Status          Status: 200
✅ SUCCESS    - Local Dashboard           Status: 200
✅ SUCCESS    - Direct IP Health          Status: 200
✅ SUCCESS    - Direct IP API Health      Status: 200
❌ FAILED     - Domain API Health         Connection refused
❌ FAILED     - Domain Dashboard          Connection refused

============================================================
🔍 Testing Webhook Endpoints
============================================================
✅ SUCCESS    - Local Webhook             Status: 200
✅ SUCCESS    - Local API Webhook         Status: 200
✅ SUCCESS    - Direct IP Webhook         Status: 200
❌ FAILED     - Domain API Webhook        Connection refused

============================================================
🔍 Test Summary
============================================================
📊 API Endpoints:     7/9 passed
📊 Webhook Endpoints: 3/4 passed
📊 Overall:           10/13 passed
📊 Success Rate:      76.9%

============================================================
🔍 Recommendations
============================================================
🔧 API Issues Found:
   - Check if API server is running: ps aux | grep api_server
   - Check API logs: tail -f logs/api_server.log
   - Restart API server: ./deploy_production.sh
🔧 Webhook Issues Found:
   - Check Nginx configuration: sudo nginx -t
   - Check Nginx logs: sudo tail -f /var/log/nginx/error.log
   - Restart Nginx: sudo systemctl restart nginx
🔧 Domain Issues Found:
   - Check DNS resolution: nslookup greateatfood.com
   - Check SSL certificate: curl -I https://greateatfood.com
   - Check firewall: sudo ufw status

⏰ Test completed at: 2025-06-13 19:37:46
⚠️  Some tests failed. Check the issues above.
(venv) ubuntu@ip-172-31-37-32:/home/<USER>/xauusd-ai-system$ Restart Nginx: sudo systemctl restart nginx
Restart: command not found
(venv) ubuntu@ip-172-31-37-32:/home/<USER>/xauusd-ai-system$ sudo systemctl restart nginx
(venv) ubuntu@ip-172-31-37-32:/home/<USER>/xauusd-ai-system$ sudo ufw status
Status: active

To                         Action      From
--                         ------      ----
22/tcp                     ALLOW       Anywhere
80                         ALLOW       Anywhere
443                        ALLOW       Anywhere
8000                       ALLOW       Anywhere
80/tcp                     ALLOW       Anywhere
443/tcp                    ALLOW       Anywhere
8000/tcp                   ALLOW       Anywhere
3000/tcp                   ALLOW       Anywhere
22/tcp (v6)                ALLOW       Anywhere (v6)
80 (v6)                    ALLOW       Anywhere (v6)
443 (v6)                   ALLOW       Anywhere (v6)
8000 (v6)                  ALLOW       Anywhere (v6)
80/tcp (v6)                ALLOW       Anywhere (v6)
443/tcp (v6)               ALLOW       Anywhere (v6)
8000/tcp (v6)              ALLOW       Anywhere (v6)
3000/tcp (v6)              ALLOW       Anywhere (v6)

(venv) ubuntu@ip-172-31-37-32:/home/<USER>/xauusd-ai-system$
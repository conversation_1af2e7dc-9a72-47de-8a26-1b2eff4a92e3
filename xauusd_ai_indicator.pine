//@version=5
indicator("XAUUSD AI Trading System", shorttitle="XAUUSD AI", overlay=true)

// ============================================================================
// XAUUSD AI Trading System - TradingView Pine Script Indicator
// Connects to your production API at greateatfood.com
// ============================================================================

// Input Parameters
webhook_url = input.string("https://greateatfood.com/api/webhook/tradingview", title="Webhook URL")
enable_alerts = input.bool(true, title="Enable Webhook Alerts")
rsi_period = input.int(14, title="RSI Period", minval=1)
macd_fast = input.int(12, title="MACD Fast Length", minval=1)
macd_slow = input.int(26, title="MACD Slow Length", minval=1)
macd_signal = input.int(9, title="MACD Signal Length", minval=1)
bb_period = input.int(20, title="Bollinger Bands Period", minval=1)
bb_mult = input.float(2.0, title="Bollinger Bands Multiplier", minval=0.1)

// Technical Indicators
rsi = ta.rsi(close, rsi_period)
[macd_line, signal_line, macd_hist] = ta.macd(close, macd_fast, macd_slow, macd_signal)
bb_basis = ta.sma(close, bb_period)
bb_dev = bb_mult * ta.stdev(close, bb_period)
bb_upper = bb_basis + bb_dev
bb_lower = bb_basis - bb_dev

// Moving Averages
ema_20 = ta.ema(close, 20)
ema_50 = ta.ema(close, 50)
sma_200 = ta.sma(close, 200)

// Volume Analysis
vol_sma = ta.sma(volume, 20)
vol_ratio = volume / vol_sma

// Signal Logic
// RSI Signals
rsi_oversold = rsi < 30
rsi_overbought = rsi > 70
rsi_bullish = rsi > 50 and rsi[1] <= 50
rsi_bearish = rsi < 50 and rsi[1] >= 50

// MACD Signals
macd_bullish = macd_line > signal_line and macd_line[1] <= signal_line[1]
macd_bearish = macd_line < signal_line and macd_line[1] >= signal_line[1]

// Bollinger Bands Signals
bb_squeeze = (bb_upper - bb_lower) / bb_basis < 0.1
bb_breakout_up = close > bb_upper and close[1] <= bb_upper[1]
bb_breakout_down = close < bb_lower and close[1] >= bb_lower[1]

// Trend Analysis
trend_bullish = ema_20 > ema_50 and ema_50 > sma_200
trend_bearish = ema_20 < ema_50 and ema_50 < sma_200

// Volume Confirmation
high_volume = vol_ratio > 1.5

// Combined Signal Logic
buy_signal = (rsi_bullish or (rsi_oversold and rsi > rsi[1])) and 
             macd_bullish and 
             (trend_bullish or bb_breakout_up) and
             high_volume

sell_signal = (rsi_bearish or (rsi_overbought and rsi < rsi[1])) and 
              macd_bearish and 
              (trend_bearish or bb_breakout_down) and
              high_volume

// Signal Strength Calculation
signal_strength = 0.0
if buy_signal or sell_signal
    strength_factors = 0
    total_factors = 6
    
    // RSI factor
    if (buy_signal and rsi_oversold) or (sell_signal and rsi_overbought)
        strength_factors += 1
    
    // MACD factor
    if (buy_signal and macd_bullish) or (sell_signal and macd_bearish)
        strength_factors += 1
    
    // Trend factor
    if (buy_signal and trend_bullish) or (sell_signal and trend_bearish)
        strength_factors += 1
    
    // Volume factor
    if high_volume
        strength_factors += 1
    
    // Bollinger factor
    if (buy_signal and bb_breakout_up) or (sell_signal and bb_breakout_down)
        strength_factors += 1
    
    // Price action factor
    if (buy_signal and close > open) or (sell_signal and close < open)
        strength_factors += 1
    
    signal_strength := strength_factors / total_factors

// Plotting
plotshape(buy_signal, title="Buy Signal", location=location.belowbar, color=color.green, style=shape.labelup, text="BUY", size=size.normal)
plotshape(sell_signal, title="Sell Signal", location=location.abovebar, color=color.red, style=shape.labeldown, text="SELL", size=size.normal)

// Plot indicators
plot(ema_20, title="EMA 20", color=color.blue, linewidth=1)
plot(ema_50, title="EMA 50", color=color.orange, linewidth=1)
plot(sma_200, title="SMA 200", color=color.gray, linewidth=2)

// Bollinger Bands
p1 = plot(bb_upper, title="BB Upper", color=color.purple, linewidth=1)
p2 = plot(bb_lower, title="BB Lower", color=color.purple, linewidth=1)
fill(p1, p2, color=color.new(color.purple, 95), title="BB Fill")

// Background coloring for trend
bgcolor(trend_bullish ? color.new(color.green, 95) : trend_bearish ? color.new(color.red, 95) : na, title="Trend Background")

// Create JSON message for webhook
create_webhook_message(signal_type, price, confidence) =>
    timestamp = str.format("{0,date,yyyy-MM-dd}T{0,time,HH:mm:ss}Z", timenow)

    // Indicator data - simplified to avoid line continuation issues
    bb_pos = close > bb_upper ? "above_upper" : close < bb_lower ? "below_lower" : "middle"
    trend_dir = trend_bullish ? "bullish" : trend_bearish ? "bearish" : "neutral"

    indicators = '{"rsi": ' + str.tostring(rsi) + ', "macd_line": ' + str.tostring(macd_line) +
                 ', "macd_signal": ' + str.tostring(signal_line) + ', "bb_position": "' + bb_pos +
                 '", "trend": "' + trend_dir + '", "volume_ratio": ' + str.tostring(vol_ratio) + '}'

    // Main message
    message = '{"signal": "' + signal_type + '", "price": ' + str.tostring(price) +
              ', "timeframe": "' + timeframe.period + '", "timestamp": "' + timestamp +
              '", "confidence": ' + str.tostring(confidence) + ', "indicator_data": ' + indicators + '}'
    message

// Alert conditions and webhook calls
if buy_signal and enable_alerts
    webhook_message = create_webhook_message("BUY", close, signal_strength)
    alert(webhook_message, alert.freq_once_per_bar)

if sell_signal and enable_alerts
    webhook_message = create_webhook_message("SELL", close, signal_strength)
    alert(webhook_message, alert.freq_once_per_bar)

// Table for signal information
if barstate.islast
    var table info_table = table.new(position.top_right, 2, 8, bgcolor=color.white, border_width=1)
    
    table.cell(info_table, 0, 0, "XAUUSD AI System", text_color=color.black, text_size=size.normal)
    table.cell(info_table, 1, 0, "Status: " + (enable_alerts ? "🟢 Active" : "🔴 Inactive"), text_color=color.black)
    
    table.cell(info_table, 0, 1, "RSI", text_color=color.black)
    table.cell(info_table, 1, 1, str.tostring(math.round(rsi, 2)), text_color=rsi_overbought ? color.red : rsi_oversold ? color.green : color.black)
    
    table.cell(info_table, 0, 2, "MACD", text_color=color.black)
    table.cell(info_table, 1, 2, macd_line > signal_line ? "🟢 Bull" : "🔴 Bear", text_color=color.black)
    
    table.cell(info_table, 0, 3, "Trend", text_color=color.black)
    table.cell(info_table, 1, 3, trend_bullish ? "🟢 Bull" : trend_bearish ? "🔴 Bear" : "🟡 Neutral", text_color=color.black)
    
    table.cell(info_table, 0, 4, "Volume", text_color=color.black)
    table.cell(info_table, 1, 4, high_volume ? "🔥 High" : "📊 Normal", text_color=color.black)
    
    table.cell(info_table, 0, 5, "BB Position", text_color=color.black)
    bb_pos = close > bb_upper ? "🔴 Above" : close < bb_lower ? "🟢 Below" : "🟡 Middle"
    table.cell(info_table, 1, 5, bb_pos, text_color=color.black)
    
    table.cell(info_table, 0, 6, "Last Signal", text_color=color.black)
    last_signal_text = buy_signal ? "🟢 BUY" : sell_signal ? "🔴 SELL" : "🟡 HOLD"
    table.cell(info_table, 1, 6, last_signal_text, text_color=color.black)
    
    table.cell(info_table, 0, 7, "Confidence", text_color=color.black)
    confidence_text = (buy_signal or sell_signal) ? str.tostring(math.round(signal_strength * 100, 1)) + "%" : "N/A"
    table.cell(info_table, 1, 7, confidence_text, text_color=color.black)

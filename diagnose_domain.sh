#!/bin/bash

# XAUUSD AI System - Domain Diagnosis Script
# This script diagnoses domain connectivity issues

echo "🔍 XAUUSD AI System - Domain Diagnosis"
echo "======================================"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "\n${BLUE}$1${NC}"
    echo "$(printf '=%.0s' {1..50})"
}

print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_header "1. DNS Resolution Test"
echo "Testing greateatfood.com DNS resolution..."
nslookup greateatfood.com
echo ""
dig greateatfood.com A +short

print_header "2. Network Connectivity Test"
echo "Testing ping to greateatfood.com..."
ping -c 3 greateatfood.com

print_header "3. Port Connectivity Test"
echo "Testing port 80 connectivity..."
nc -zv greateatfood.com 80 2>&1

echo "Testing port 443 connectivity..."
nc -zv greateatfood.com 443 2>&1

print_header "4. HTTP Response Test"
echo "Testing HTTP response from greateatfood.com..."
curl -I -m 10 http://greateatfood.com 2>&1

print_header "5. Local Services Status"
echo "API Server status:"
ps aux | grep api_server | grep -v grep
echo ""

echo "Nginx status:"
sudo systemctl status nginx --no-pager -l

echo ""
echo "Ports in use:"
sudo netstat -tlnp | grep -E ':80|:443|:8000|:3000'

print_header "6. Nginx Configuration Check"
echo "Current Nginx sites enabled:"
ls -la /etc/nginx/sites-enabled/

echo ""
echo "Nginx configuration test:"
sudo nginx -t

print_header "7. Firewall Status"
sudo ufw status verbose

print_header "8. System Resources"
echo "Memory usage:"
free -h

echo ""
echo "Disk usage:"
df -h

echo ""
echo "Load average:"
uptime

print_header "9. Recent Nginx Logs"
echo "Last 10 lines of Nginx error log:"
sudo tail -10 /var/log/nginx/error.log

echo ""
echo "Last 10 lines of Nginx access log:"
sudo tail -10 /var/log/nginx/access.log

print_header "10. Domain Ownership Verification"
echo "Checking if domain points to this server..."
DOMAIN_IP=$(dig +short greateatfood.com)
SERVER_IP=$(curl -s ifconfig.me)

echo "Domain IP: $DOMAIN_IP"
echo "Server IP: $SERVER_IP"

if [ "$DOMAIN_IP" = "$SERVER_IP" ]; then
    echo -e "${GREEN}✅ Domain points to this server${NC}"
else
    echo -e "${RED}❌ Domain does NOT point to this server${NC}"
    echo -e "${YELLOW}⚠️  This is likely the main issue!${NC}"
fi

print_header "Diagnosis Summary"
echo "=================="

# Check if domain resolves
if nslookup greateatfood.com > /dev/null 2>&1; then
    echo "✅ Domain resolves"
else
    echo "❌ Domain resolution failed"
fi

# Check if Nginx is running
if sudo systemctl is-active --quiet nginx; then
    echo "✅ Nginx is running"
else
    echo "❌ Nginx is not running"
fi

# Check if API server is running
if pgrep -f api_server > /dev/null; then
    echo "✅ API server is running"
else
    echo "❌ API server is not running"
fi

# Check if ports are open
if sudo netstat -tlnp | grep -q ':80'; then
    echo "✅ Port 80 is open"
else
    echo "❌ Port 80 is not open"
fi

echo ""
echo "🔧 Recommended Actions:"
if [ "$DOMAIN_IP" != "$SERVER_IP" ]; then
    echo "1. ⚠️  CRITICAL: Update DNS records to point greateatfood.com to $SERVER_IP"
fi

if ! sudo systemctl is-active --quiet nginx; then
    echo "2. Restart Nginx: sudo systemctl restart nginx"
fi

if ! pgrep -f api_server > /dev/null; then
    echo "3. Restart API server: ./deploy_production.sh"
fi

echo "4. Wait for DNS propagation (can take up to 24 hours)"
echo "5. Test again with: python3 test_connections.py"

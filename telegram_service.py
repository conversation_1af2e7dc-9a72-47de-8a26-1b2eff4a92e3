"""
Telegram Notification Service for XAUUSD AI Trading System
Sends real-time trading signals and system alerts via Telegram
"""

import os
import asyncio
import aiohttp
import json
from datetime import datetime
from typing import Dict, Any, Optional
from loguru import logger
import sys
from dataclasses import dataclass

# Setup logging
logger.remove()
logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>")
logger.add("logs/telegram.log", rotation="10 MB", level="DEBUG")

@dataclass
class TelegramConfig:
    """Telegram configuration"""
    bot_token: str
    chat_id: str
    parse_mode: str = "HTML"
    disable_web_page_preview: bool = True

class TelegramNotificationService:
    """Telegram notification service for trading signals"""
    
    def __init__(self, bot_token: str, chat_id: str):
        self.config = TelegramConfig(bot_token=bot_token, chat_id=chat_id)
        self.base_url = f"https://api.telegram.org/bot{bot_token}"
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def send_message(self, message: str, parse_mode: str = None) -> bool:
        """Send a message to Telegram"""
        try:
            url = f"{self.base_url}/sendMessage"
            
            payload = {
                "chat_id": self.config.chat_id,
                "text": message,
                "parse_mode": parse_mode or self.config.parse_mode,
                "disable_web_page_preview": self.config.disable_web_page_preview
            }
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    logger.info("✅ Telegram message sent successfully")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Failed to send Telegram message: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Error sending Telegram message: {e}")
            return False
    
    async def send_trading_signal(self, signal_data: Dict[str, Any]) -> bool:
        """Send a formatted trading signal message"""
        try:
            # Extract signal information
            signal = signal_data.get('signal', 'UNKNOWN')
            confidence = signal_data.get('confidence', 0) * 100
            price = signal_data.get('current_price', 0)
            timeframe = signal_data.get('timeframe', '1h')
            timestamp = signal_data.get('timestamp', datetime.now().isoformat())
            model_type = signal_data.get('model_type', 'LSTM')
            
            # Market context
            context = signal_data.get('market_context', {})
            trend = context.get('trend', 'unknown')
            volatility = context.get('volatility', 'normal')
            rsi_level = context.get('rsi_level', 'neutral')
            
            # Signal emoji
            signal_emoji = "🟢" if signal == "BUY" else "🔴" if signal == "SELL" else "⚪"
            confidence_emoji = "🔥" if confidence >= 80 else "⚡" if confidence >= 70 else "💡"
            
            # Format message
            message = f"""
🤖 <b>XAUUSD AI Trading Signal</b> {signal_emoji}

📊 <b>Signal Details:</b>
• <b>Action:</b> {signal}
• <b>Confidence:</b> {confidence:.1f}% {confidence_emoji}
• <b>Price:</b> ${price:.2f}
• <b>Timeframe:</b> {timeframe}
• <b>Model:</b> {model_type}

📈 <b>Market Context:</b>
• <b>Trend:</b> {trend.title()}
• <b>Volatility:</b> {volatility.title()}
• <b>RSI Level:</b> {rsi_level.title()}

⏰ <b>Time:</b> {datetime.fromisoformat(timestamp.replace('Z', '')).strftime('%Y-%m-%d %H:%M:%S')}

💡 <i>Generated by XAUUSD AI Trading System</i>
            """.strip()
            
            return await self.send_message(message)
            
        except Exception as e:
            logger.error(f"❌ Error formatting trading signal: {e}")
            return False
    
    async def send_system_alert(self, alert_type: str, message: str, severity: str = "info") -> bool:
        """Send a system alert message"""
        try:
            # Severity emojis
            severity_emojis = {
                "info": "ℹ️",
                "warning": "⚠️",
                "error": "❌",
                "success": "✅",
                "critical": "🚨"
            }
            
            emoji = severity_emojis.get(severity.lower(), "ℹ️")
            
            formatted_message = f"""
{emoji} <b>System Alert</b>

<b>Type:</b> {alert_type}
<b>Severity:</b> {severity.upper()}

<b>Message:</b>
{message}

⏰ <b>Time:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """.strip()
            
            return await self.send_message(formatted_message)
            
        except Exception as e:
            logger.error(f"❌ Error sending system alert: {e}")
            return False
    
    async def send_daily_summary(self, summary_data: Dict[str, Any]) -> bool:
        """Send daily trading summary"""
        try:
            total_signals = summary_data.get('total_signals', 0)
            buy_signals = summary_data.get('buy_signals', 0)
            sell_signals = summary_data.get('sell_signals', 0)
            avg_confidence = summary_data.get('avg_confidence', 0) * 100
            price_change = summary_data.get('price_change', 0)
            
            price_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
            
            message = f"""
📊 <b>Daily Trading Summary</b>

📈 <b>Signal Statistics:</b>
• <b>Total Signals:</b> {total_signals}
• <b>Buy Signals:</b> {buy_signals} 🟢
• <b>Sell Signals:</b> {sell_signals} 🔴
• <b>Avg Confidence:</b> {avg_confidence:.1f}%

💰 <b>Market Performance:</b>
• <b>Price Change:</b> {price_change:+.2f}% {price_emoji}

📅 <b>Date:</b> {datetime.now().strftime('%Y-%m-%d')}

🤖 <i>XAUUSD AI Trading System</i>
            """.strip()
            
            return await self.send_message(message)
            
        except Exception as e:
            logger.error(f"❌ Error sending daily summary: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """Test Telegram bot connection"""
        try:
            url = f"{self.base_url}/getMe"
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    bot_info = data.get('result', {})
                    bot_name = bot_info.get('first_name', 'Unknown')
                    logger.info(f"✅ Telegram bot connection successful: {bot_name}")
                    
                    # Send test message
                    test_message = f"""
🤖 <b>Connection Test Successful!</b>

✅ Bot is connected and ready to send notifications.

⏰ <b>Test Time:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                    """.strip()
                    
                    return await self.send_message(test_message)
                else:
                    logger.error(f"❌ Telegram bot connection failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Error testing Telegram connection: {e}")
            return False

class TelegramManager:
    """Manager class for Telegram notifications"""
    
    def __init__(self):
        self.service = None
        self.config_loaded = False
        
    def load_config(self) -> bool:
        """Load Telegram configuration from environment"""
        try:
            bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
            chat_id = os.getenv('TELEGRAM_CHAT_ID')
            
            if not bot_token or not chat_id:
                logger.warning("⚠️ Telegram configuration not found in environment variables")
                logger.info("Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID")
                return False
            
            self.service = TelegramNotificationService(bot_token, chat_id)
            self.config_loaded = True
            logger.info("✅ Telegram configuration loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error loading Telegram configuration: {e}")
            return False
    
    async def send_signal_notification(self, signal_data: Dict[str, Any]) -> bool:
        """Send signal notification if configured"""
        if not self.config_loaded:
            logger.debug("Telegram not configured, skipping notification")
            return False
        
        try:
            async with self.service as telegram:
                return await telegram.send_trading_signal(signal_data)
        except Exception as e:
            logger.error(f"❌ Error sending signal notification: {e}")
            return False
    
    async def send_alert(self, alert_type: str, message: str, severity: str = "info") -> bool:
        """Send system alert if configured"""
        if not self.config_loaded:
            logger.debug("Telegram not configured, skipping alert")
            return False
        
        try:
            async with self.service as telegram:
                return await telegram.send_system_alert(alert_type, message, severity)
        except Exception as e:
            logger.error(f"❌ Error sending alert: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """Test Telegram connection"""
        if not self.config_loaded:
            if not self.load_config():
                return False
        
        try:
            async with self.service as telegram:
                return await telegram.test_connection()
        except Exception as e:
            logger.error(f"❌ Error testing connection: {e}")
            return False

# Global telegram manager instance
telegram_manager = TelegramManager()

async def send_trading_signal(signal_data: Dict[str, Any]) -> bool:
    """Convenience function to send trading signal"""
    return await telegram_manager.send_signal_notification(signal_data)

async def send_system_alert(alert_type: str, message: str, severity: str = "info") -> bool:
    """Convenience function to send system alert"""
    return await telegram_manager.send_alert(alert_type, message, severity)

async def test_telegram_connection() -> bool:
    """Convenience function to test Telegram connection"""
    return await telegram_manager.test_connection()

def setup_telegram():
    """Setup Telegram service"""
    return telegram_manager.load_config()

# Example usage and testing
async def main():
    """Test the Telegram service"""
    logger.info("🧪 Testing Telegram Notification Service")
    
    # Load configuration
    if not setup_telegram():
        logger.error("❌ Failed to setup Telegram service")
        return
    
    # Test connection
    logger.info("🔗 Testing connection...")
    if await test_telegram_connection():
        logger.info("✅ Connection test successful")
    else:
        logger.error("❌ Connection test failed")
        return
    
    # Test trading signal
    logger.info("📊 Testing trading signal...")
    test_signal = {
        "signal": "BUY",
        "confidence": 0.857,
        "current_price": 3457.60,
        "timeframe": "1h",
        "timestamp": datetime.now().isoformat(),
        "model_type": "Enhanced LSTM",
        "market_context": {
            "trend": "bullish",
            "volatility": "normal",
            "rsi_level": "oversold"
        }
    }
    
    if await send_trading_signal(test_signal):
        logger.info("✅ Trading signal test successful")
    else:
        logger.error("❌ Trading signal test failed")
    
    # Test system alert
    logger.info("🚨 Testing system alert...")
    if await send_system_alert("System Test", "This is a test alert from the XAUUSD AI system", "info"):
        logger.info("✅ System alert test successful")
    else:
        logger.error("❌ System alert test failed")

if __name__ == "__main__":
    asyncio.run(main())

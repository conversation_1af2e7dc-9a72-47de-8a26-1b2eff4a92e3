#!/bin/bash

# XAUUSD AI System - Nginx Configuration Fix
# This script fixes the Nginx configuration for domain access

echo "🔧 XAUUSD AI System - Nginx Configuration Fix"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Step 1: Check current Nginx configuration
print_info "Step 1: Checking current Nginx configuration..."

if [ -f "/etc/nginx/sites-available/xauusd-ai" ]; then
    print_status "Found existing Nginx configuration"
    echo "Current configuration:"
    sudo cat /etc/nginx/sites-available/xauusd-ai
else
    print_error "Nginx configuration file not found!"
    print_info "Creating new configuration..."
fi

echo ""
print_info "Step 2: Creating updated Nginx configuration..."

# Create the updated Nginx configuration
sudo tee /etc/nginx/sites-available/xauusd-ai > /dev/null << 'EOF'
server {
    listen 80;
    server_name greateatfood.com www.greateatfood.com;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # API endpoints - Proxy to backend
    location /api/ {
        proxy_pass http://127.0.0.1:8000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # CORS headers
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;
    }

    # Health check endpoint (direct)
    location /health {
        proxy_pass http://127.0.0.1:8000/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Dashboard
    location /dashboard/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Root redirect to dashboard
    location / {
        return 301 /dashboard/;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
}
EOF

print_status "Updated Nginx configuration created"

# Step 3: Test Nginx configuration
print_info "Step 3: Testing Nginx configuration..."
if sudo nginx -t; then
    print_status "Nginx configuration is valid"
else
    print_error "Nginx configuration has errors!"
    exit 1
fi

# Step 4: Enable the site
print_info "Step 4: Enabling the site..."
sudo ln -sf /etc/nginx/sites-available/xauusd-ai /etc/nginx/sites-enabled/
print_status "Site enabled"

# Step 5: Remove default site if it exists
if [ -f "/etc/nginx/sites-enabled/default" ]; then
    print_info "Removing default Nginx site..."
    sudo rm /etc/nginx/sites-enabled/default
    print_status "Default site removed"
fi

# Step 6: Restart Nginx
print_info "Step 6: Restarting Nginx..."
sudo systemctl restart nginx

if sudo systemctl is-active --quiet nginx; then
    print_status "Nginx restarted successfully"
else
    print_error "Nginx failed to restart!"
    print_info "Checking Nginx status..."
    sudo systemctl status nginx
    exit 1
fi

# Step 7: Check firewall
print_info "Step 7: Checking firewall configuration..."
sudo ufw status

# Step 8: Test domain resolution
print_info "Step 8: Testing domain resolution..."
if nslookup greateatfood.com > /dev/null 2>&1; then
    print_status "Domain resolves correctly"
    nslookup greateatfood.com | grep "Address:"
else
    print_warning "Domain resolution might have issues"
fi

# Step 9: Test endpoints
print_info "Step 9: Testing endpoints..."

echo "Testing local API..."
if curl -s http://localhost:8000/api/health > /dev/null; then
    print_status "Local API working"
else
    print_warning "Local API not responding"
fi

echo "Testing domain API..."
if curl -s -m 10 http://greateatfood.com/api/health > /dev/null; then
    print_status "Domain API working"
else
    print_warning "Domain API not responding yet (may need DNS propagation)"
fi

echo ""
print_info "Configuration Summary:"
echo "======================"
echo "✅ Nginx configuration updated"
echo "✅ Site enabled and default disabled"
echo "✅ Nginx restarted"
echo ""
echo "🌐 Test these URLs:"
echo "   http://greateatfood.com/api/health"
echo "   http://greateatfood.com/api/webhook/tradingview"
echo "   http://greateatfood.com/dashboard/"
echo ""
print_info "If still not working, check:"
echo "1. DNS propagation: nslookup greateatfood.com"
echo "2. Nginx logs: sudo tail -f /var/log/nginx/error.log"
echo "3. Firewall: sudo ufw status"
echo "4. API server: ps aux | grep api_server"

@echo off
REM Upload script for <PERSON> to deploy XAUUSD AI fixes to production server
REM Make sure you have your SSH key ready

echo 🚀 XAUUSD AI System - File Upload Script
echo ========================================

REM Set your SSH key path here
set SSH_KEY=your-key.pem
set SERVER=ubuntu@************
set TARGET_DIR=/home/<USER>/xauusd-ai-system

echo 📤 Uploading updated files to production server...

REM Upload core updated files
echo Uploading api_server.py...
scp -i %SSH_KEY% api_server.py %SERVER%:%TARGET_DIR%/

echo Uploading .env configuration...
scp -i %SSH_KEY% .env %SERVER%:%TARGET_DIR%/

REM Upload new deployment scripts
echo Uploading cleanup_processes.sh...
scp -i %SSH_KEY% cleanup_processes.sh %SERVER%:%TARGET_DIR%/

echo Uploading deploy_production.sh...
scp -i %SSH_KEY% deploy_production.sh %SERVER%:%TARGET_DIR%/

echo Uploading test_connections.py...
scp -i %SSH_KEY% test_connections.py %SERVER%:%TARGET_DIR%/

echo Uploading deployment guide...
scp -i %SSH_KEY% DEPLOYMENT_GUIDE.md %SERVER%:%TARGET_DIR%/

echo ✅ File upload completed!
echo.
echo 🔧 Next steps:
echo 1. Connect to server: ssh -i %SSH_KEY% %SERVER%
echo 2. Switch user: sudo su - xauusd-ai
echo 3. Go to directory: cd /home/<USER>/xauusd-ai-system
echo 4. Make scripts executable: chmod +x *.sh *.py
echo 5. Run deployment: ./deploy_production.sh
echo 6. Test connections: python3 test_connections.py
echo.
pause

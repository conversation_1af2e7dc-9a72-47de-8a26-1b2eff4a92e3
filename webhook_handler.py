"""
Webhook Handler for TradingView Integration
"""
import json
import hmac
import hashlib
from datetime import datetime
from typing import Dict, Optional
from fastapi import HTTPException
from loguru import logger
import requests

from config import settings


class WebhookHandler:
    """Handles webhook communication between TradingView and AI system"""
    
    def __init__(self):
        self.webhook_secret = settings.WEBHOOK_SECRET
        self.tradingview_url = settings.TRADINGVIEW_WEBHOOK_URL
        
    def verify_webhook_signature(self, payload: str, signature: str) -> bool:
        """Verify webhook signature for security"""
        try:
            expected_signature = hmac.new(
                self.webhook_secret.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(signature, expected_signature)
            
        except Exception as e:
            logger.error(f"Error verifying webhook signature: {e}")
            return False
    
    def parse_tradingview_webhook(self, payload: Dict) -> Optional[Dict]:
        """Parse incoming webhook from TradingView"""
        try:
            # Expected TradingView webhook format
            required_fields = ['symbol', 'timeframe', 'close', 'volume']
            
            # Validate required fields
            for field in required_fields:
                if field not in payload:
                    logger.error(f"Missing required field in webhook: {field}")
                    return None
            
            # Extract market data
            market_data = {
                'symbol': payload['symbol'],
                'timeframe': payload['timeframe'],
                'timestamp': payload.get('timestamp', datetime.now().isoformat()),
                'price_data': {
                    'open': payload.get('open', payload['close']),
                    'high': payload.get('high', payload['close']),
                    'low': payload.get('low', payload['close']),
                    'close': payload['close'],
                    'volume': payload['volume']
                },
                'indicators': payload.get('indicators', {}),
                'alert_message': payload.get('alert_message', ''),
                'strategy': payload.get('strategy', 'default')
            }
            
            logger.info(f"Parsed TradingView webhook for {market_data['symbol']} {market_data['timeframe']}")
            return market_data
            
        except Exception as e:
            logger.error(f"Error parsing TradingView webhook: {e}")
            return None
    
    def send_signal_to_tradingview(self, signal: Dict) -> bool:
        """Send AI-generated signal back to TradingView"""
        try:
            if not self.tradingview_url:
                logger.warning("TradingView webhook URL not configured")
                return False
            
            # Prepare webhook payload for TradingView
            webhook_payload = {
                'timestamp': signal.get('timestamp', datetime.now().isoformat()),
                'symbol': 'XAUUSD',
                'timeframe': signal.get('timeframe', '1h'),
                'action': signal['signal'].lower(),  # 'buy' or 'sell'
                'confidence': signal['confidence'],
                'price': signal.get('current_price', 0),
                'model_type': signal.get('model_type', 'ai'),
                'message': self._create_alert_message(signal),
                'secret': self.webhook_secret
            }
            
            # Add market context if available
            if 'market_context' in signal:
                webhook_payload['context'] = signal['market_context']
            
            # Send webhook
            response = requests.post(
                self.tradingview_url,
                json=webhook_payload,
                timeout=10,
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'XAUUSD-AI-System/1.0'
                }
            )
            
            if response.status_code == 200:
                logger.info(f"Signal sent to TradingView: {signal['signal']} (confidence: {signal['confidence']:.2%})")
                return True
            else:
                logger.error(f"Failed to send signal to TradingView: HTTP {response.status_code}")
                logger.error(f"Response: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            logger.error("Timeout sending signal to TradingView")
            return False
        except requests.exceptions.ConnectionError:
            logger.error("Connection error sending signal to TradingView")
            return False
        except Exception as e:
            logger.error(f"Error sending signal to TradingView: {e}")
            return False
    
    def _create_alert_message(self, signal: Dict) -> str:
        """Create formatted alert message for TradingView"""
        try:
            action = signal['signal']
            confidence = signal['confidence']
            price = signal.get('current_price', 0)
            timeframe = signal.get('timeframe', '1h')
            
            # Base message
            message = f"🤖 XAUUSD AI {action} SIGNAL\n"
            message += f"📊 Timeframe: {timeframe}\n"
            message += f"💰 Price: ${price:.2f}\n"
            message += f"🎯 Confidence: {confidence:.1%}\n"
            
            # Add market context if available
            if 'market_context' in signal:
                context = signal['market_context']
                message += f"📈 Trend: {context.get('trend', 'N/A').title()}\n"
                message += f"📊 RSI: {context.get('rsi_level', 'N/A').title()}\n"
                message += f"💹 Volatility: {context.get('volatility', 'N/A').title()}\n"
            
            # Add timestamp
            timestamp = signal.get('timestamp', datetime.now().isoformat())
            message += f"⏰ Time: {timestamp[:19].replace('T', ' ')}"
            
            return message
            
        except Exception as e:
            logger.error(f"Error creating alert message: {e}")
            return f"XAUUSD AI {signal.get('signal', 'SIGNAL')} - Confidence: {signal.get('confidence', 0):.1%}"
    
    def create_tradingview_alert_syntax(self, signal: Dict) -> str:
        """Create TradingView alert syntax for automated trading"""
        try:
            action = signal['signal'].lower()
            confidence = signal['confidence']
            price = signal.get('current_price', 0)
            
            # TradingView alert syntax for automated trading
            alert_syntax = {
                'action': action,
                'symbol': 'XAUUSD',
                'price': price,
                'confidence': confidence,
                'timeframe': signal.get('timeframe', '1h'),
                'timestamp': signal.get('timestamp', datetime.now().isoformat())
            }
            
            # Convert to JSON string for TradingView
            return json.dumps(alert_syntax, separators=(',', ':'))
            
        except Exception as e:
            logger.error(f"Error creating TradingView alert syntax: {e}")
            return json.dumps({'action': 'hold', 'error': str(e)})
    
    def validate_webhook_payload(self, payload: Dict) -> bool:
        """Validate incoming webhook payload"""
        try:
            # Check for required fields
            required_fields = ['symbol', 'timeframe', 'close']
            for field in required_fields:
                if field not in payload:
                    logger.error(f"Missing required field: {field}")
                    return False
            
            # Validate symbol
            if payload['symbol'] not in ['XAUUSD', 'GOLD', 'XAU/USD']:
                logger.error(f"Invalid symbol: {payload['symbol']}")
                return False
            
            # Validate timeframe
            valid_timeframes = ['15m', '1h', '4h', '1d']
            if payload['timeframe'] not in valid_timeframes:
                logger.error(f"Invalid timeframe: {payload['timeframe']}")
                return False
            
            # Validate price data
            try:
                price = float(payload['close'])
                if price <= 0 or price > 10000:  # Reasonable range for gold price
                    logger.error(f"Invalid price: {price}")
                    return False
            except (ValueError, TypeError):
                logger.error(f"Invalid price format: {payload['close']}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating webhook payload: {e}")
            return False
    
    def process_webhook_data(self, payload: Dict) -> Optional[Dict]:
        """Process webhook data and prepare for AI analysis"""
        try:
            # Validate payload
            if not self.validate_webhook_payload(payload):
                return None
            
            # Parse webhook data
            market_data = self.parse_tradingview_webhook(payload)
            if not market_data:
                return None
            
            # Add processing timestamp
            market_data['processed_at'] = datetime.now().isoformat()
            
            # Add webhook metadata
            market_data['webhook_metadata'] = {
                'source': 'tradingview',
                'version': '1.0',
                'processed_by': 'xauusd_ai_system'
            }
            
            logger.info(f"Processed webhook data for {market_data['symbol']} {market_data['timeframe']}")
            return market_data
            
        except Exception as e:
            logger.error(f"Error processing webhook data: {e}")
            return None
    
    def create_webhook_response(self, success: bool, message: str = "", data: Dict = None) -> Dict:
        """Create standardized webhook response"""
        response = {
            'success': success,
            'timestamp': datetime.now().isoformat(),
            'message': message
        }
        
        if data:
            response['data'] = data
        
        return response


# Webhook handler instance
webhook_handler = WebhookHandler()


def handle_tradingview_webhook(payload: Dict, signature: str = None) -> Dict:
    """Main function to handle TradingView webhooks"""
    try:
        logger.info("Received TradingView webhook")
        
        # Verify signature if provided
        if signature and not webhook_handler.verify_webhook_signature(
            json.dumps(payload), signature
        ):
            logger.error("Invalid webhook signature")
            raise HTTPException(status_code=401, detail="Invalid signature")
        
        # Process webhook data
        processed_data = webhook_handler.process_webhook_data(payload)
        
        if not processed_data:
            logger.error("Failed to process webhook data")
            return webhook_handler.create_webhook_response(
                False, "Failed to process webhook data"
            )
        
        # Here you would typically:
        # 1. Store the data for AI analysis
        # 2. Trigger signal generation
        # 3. Send response back to TradingView
        
        logger.info("Webhook processed successfully")
        return webhook_handler.create_webhook_response(
            True, "Webhook processed successfully", processed_data
        )
        
    except Exception as e:
        logger.error(f"Error handling TradingView webhook: {e}")
        return webhook_handler.create_webhook_response(
            False, f"Error processing webhook: {str(e)}"
        )


if __name__ == "__main__":
    # Test webhook handler
    test_payload = {
        'symbol': 'XAUUSD',
        'timeframe': '1h',
        'close': 2045.50,
        'volume': 1000,
        'timestamp': datetime.now().isoformat()
    }
    
    logger.info("Testing webhook handler...")
    result = handle_tradingview_webhook(test_payload)
    logger.info(f"Test result: {result}")
    
    # Test signal sending
    test_signal = {
        'signal': 'BUY',
        'confidence': 0.75,
        'current_price': 2045.50,
        'timeframe': '1h',
        'timestamp': datetime.now().isoformat(),
        'market_context': {
            'trend': 'bullish',
            'rsi_level': 'neutral',
            'volatility': 'normal'
        }
    }
    
    logger.info("Testing signal sending...")
    webhook_handler.send_signal_to_tradingview(test_signal)

2025-06-13 19:51:10 | INFO     | __main__:main:245 - XAUUSD AI Model Training Started
2025-06-13 19:51:10 | INFO     | __main__:main:246 - Configuration: timeframe=1h, model_type=lstm
2025-06-13 19:51:10 | INFO     | __main__:train_single_model:38 - Starting training for 1h timeframe with lstm model
2025-06-13 19:51:10 | INFO     | __main__:train_single_model:41 - Collecting and preprocessing data...
2025-06-13 19:51:10 | INFO     | data_collector:fetch_yfinance_data:47 - Fetched 5739 records for 1h timeframe
2025-06-13 19:51:11 | INFO     | data_collector:calculate_technical_indicators:120 - Technical indicators calculated successfully
2025-06-13 19:51:11 | INFO     | data_collector:create_derived_features:163 - Derived features created successfully
2025-06-13 19:51:11 | INFO     | data_collector:create_labels:195 - Labels created successfully
2025-06-13 19:51:11 | INFO     | data_collector:preprocess_data:221 - Data preprocessing completed. Final shape: (5689, 43)
2025-06-13 19:51:11 | INFO     | data_collector:get_processed_data:241 - Successfully processed 5689 records for 1h
2025-06-13 19:51:11 | INFO     | __main__:train_single_model:49 - Collected 5689 data points for training
2025-06-13 19:51:11 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 19:51:11 | INFO     | __main__:train_single_model:55 - Starting model training...
2025-06-13 19:51:11 | ERROR    | ai_model:prepare_sequences:123 - Error preparing sequences: float() argument must be a string or a real number, not 'Timestamp'
2025-06-13 19:51:11 | ERROR    | ai_model:train:195 - No data available for training
2025-06-13 19:51:11 | INFO     | __main__:train_single_model:61 - Training completed in 0.01 seconds
2025-06-13 19:51:11 | ERROR    | __main__:train_single_model:87 - Training failed - no metrics returned
2025-06-13 19:51:11 | INFO     | __main__:main:287 - Training process completed successfully
2025-06-13 19:51:44 | INFO     | __main__:main:245 - XAUUSD AI Model Training Started
2025-06-13 19:51:44 | INFO     | __main__:main:246 - Configuration: timeframe=1h, model_type=lstm
2025-06-13 19:51:44 | INFO     | __main__:train_single_model:38 - Starting training for 1h timeframe with lstm model
2025-06-13 19:51:44 | INFO     | __main__:train_single_model:41 - Collecting and preprocessing data...
2025-06-13 19:51:45 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 19:51:45 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 19:51:45 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 19:51:45 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 19:51:45 | INFO     | data_collector:preprocess_data:227 - Data preprocessing completed. Final shape: (5689, 43)
2025-06-13 19:51:45 | INFO     | data_collector:get_processed_data:247 - Successfully processed 5689 records for 1h
2025-06-13 19:51:45 | INFO     | __main__:train_single_model:49 - Collected 5689 data points for training
2025-06-13 19:51:45 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 19:51:45 | INFO     | __main__:train_single_model:55 - Starting model training...
2025-06-13 19:51:45 | ERROR    | ai_model:prepare_sequences:123 - Error preparing sequences: Input X contains infinity or a value too large for dtype('float64').
2025-06-13 19:51:45 | ERROR    | ai_model:train:195 - No data available for training
2025-06-13 19:51:45 | INFO     | __main__:train_single_model:61 - Training completed in 0.00 seconds
2025-06-13 19:51:45 | ERROR    | __main__:train_single_model:87 - Training failed - no metrics returned
2025-06-13 19:51:45 | INFO     | __main__:main:287 - Training process completed successfully
2025-06-13 19:52:09 | INFO     | __main__:main:245 - XAUUSD AI Model Training Started
2025-06-13 19:52:09 | INFO     | __main__:main:246 - Configuration: timeframe=1h, model_type=lstm
2025-06-13 19:52:09 | INFO     | __main__:train_single_model:38 - Starting training for 1h timeframe with lstm model
2025-06-13 19:52:09 | INFO     | __main__:train_single_model:41 - Collecting and preprocessing data...
2025-06-13 19:52:09 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 19:52:09 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 19:52:09 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 19:52:09 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 19:52:09 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 19:52:09 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
2025-06-13 19:52:09 | INFO     | __main__:train_single_model:49 - Collected 5489 data points for training
2025-06-13 19:52:09 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 19:52:09 | INFO     | __main__:train_single_model:55 - Starting model training...
2025-06-13 19:52:09 | INFO     | ai_model:prepare_sequences:119 - Prepared sequences: X shape (5429, 60, 39), y shape (5429,)
2025-06-13 19:52:09 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 19:52:10 | INFO     | ai_model:train:229 - Starting model training...
2025-06-13 19:52:18 | INFO     | ai_model:train:241 - Epoch 0: Train Loss: 0.4349, Train Acc: 0.8017, Val Loss: 0.3773, Val Acc: 0.8748
2025-06-13 19:53:42 | INFO     | ai_model:train:241 - Epoch 10: Train Loss: 0.3721, Train Acc: 0.8752, Val Loss: 0.3725, Val Acc: 0.8748
2025-06-13 19:53:42 | INFO     | ai_model:train:253 - Early stopping at epoch 10
2025-06-13 19:53:43 | INFO     | ai_model:train:278 - Model training completed successfully
2025-06-13 19:53:43 | INFO     | ai_model:train:279 - Validation Accuracy: 0.8748
2025-06-13 19:53:43 | INFO     | ai_model:train:280 - Validation F1-Score: 0.0000
2025-06-13 19:53:43 | INFO     | __main__:train_single_model:61 - Training completed in 93.83 seconds
2025-06-13 19:53:43 | INFO     | __main__:train_single_model:64 - Training Results:
2025-06-13 19:53:43 | INFO     | __main__:train_single_model:65 -   Validation Accuracy: 0.8748
2025-06-13 19:53:43 | INFO     | __main__:train_single_model:66 -   Validation Precision: 0.0000
2025-06-13 19:53:43 | INFO     | __main__:train_single_model:67 -   Validation Recall: 0.0000
2025-06-13 19:53:43 | INFO     | __main__:train_single_model:68 -   Validation F1-Score: 0.0000
2025-06-13 19:53:43 | INFO     | __main__:main:287 - Training process completed successfully
2025-06-13 19:53:55 | INFO     | __main__:main:245 - XAUUSD AI Model Training Started
2025-06-13 19:53:55 | INFO     | __main__:main:246 - Configuration: timeframe=1h, model_type=lstm
2025-06-13 19:53:55 | INFO     | __main__:train_single_model:38 - Starting training for 1h timeframe with lstm model
2025-06-13 19:53:55 | INFO     | __main__:train_single_model:41 - Collecting and preprocessing data...
2025-06-13 19:53:55 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 19:53:55 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 19:53:55 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 19:53:55 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 19:53:55 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 19:53:55 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
2025-06-13 19:53:55 | INFO     | __main__:train_single_model:49 - Collected 5489 data points for training
2025-06-13 19:53:55 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 19:53:55 | INFO     | __main__:train_single_model:55 - Starting model training...
2025-06-13 19:53:55 | INFO     | ai_model:prepare_sequences:119 - Prepared sequences: X shape (5429, 60, 39), y shape (5429,)
2025-06-13 19:53:55 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 19:53:56 | INFO     | ai_model:train:229 - Starting model training...
2025-06-13 19:54:04 | INFO     | ai_model:train:241 - Epoch 0: Train Loss: 0.4118, Train Acc: 0.8280, Val Loss: 0.3738, Val Acc: 0.8748
2025-06-13 19:55:28 | INFO     | ai_model:train:241 - Epoch 10: Train Loss: 0.3703, Train Acc: 0.8752, Val Loss: 0.3743, Val Acc: 0.8748
2025-06-13 19:55:28 | INFO     | ai_model:train:253 - Early stopping at epoch 10
2025-06-13 19:55:29 | INFO     | ai_model:train:278 - Model training completed successfully
2025-06-13 19:55:29 | INFO     | ai_model:train:279 - Validation Accuracy: 0.8748
2025-06-13 19:55:29 | INFO     | ai_model:train:280 - Validation F1-Score: 0.0000
2025-06-13 19:55:29 | INFO     | __main__:train_single_model:61 - Training completed in 93.33 seconds
2025-06-13 19:55:29 | INFO     | __main__:train_single_model:64 - Training Results:
2025-06-13 19:55:29 | INFO     | __main__:train_single_model:65 -   Validation Accuracy: 0.8748
2025-06-13 19:55:29 | INFO     | __main__:train_single_model:66 -   Validation Precision: 0.0000
2025-06-13 19:55:29 | INFO     | __main__:train_single_model:67 -   Validation Recall: 0.0000
2025-06-13 19:55:29 | INFO     | __main__:train_single_model:68 -   Validation F1-Score: 0.0000
2025-06-13 19:55:29 | INFO     | ai_model:save_model:379 - Model saved to models/xauusd_lstm_1h.h5
2025-06-13 19:55:29 | INFO     | __main__:train_single_model:74 - Model saved to models/xauusd_lstm_1h.h5
2025-06-13 19:55:29 | INFO     | __main__:main:287 - Training process completed successfully
2025-06-13 19:58:12 | INFO     | __main__:main:245 - XAUUSD AI Model Training Started
2025-06-13 19:58:12 | INFO     | __main__:main:246 - Configuration: timeframe=1h, model_type=lstm
2025-06-13 19:58:12 | INFO     | __main__:train_single_model:38 - Starting training for 1h timeframe with lstm model
2025-06-13 19:58:12 | INFO     | __main__:train_single_model:41 - Collecting and preprocessing data...
2025-06-13 19:58:12 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 19:58:12 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 19:58:12 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 19:58:12 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 19:58:12 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 19:58:12 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
2025-06-13 19:58:12 | INFO     | __main__:train_single_model:49 - Collected 5489 data points for training
2025-06-13 19:58:12 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 19:58:12 | INFO     | __main__:train_single_model:55 - Starting model training...
2025-06-13 19:58:13 | INFO     | ai_model:prepare_sequences:119 - Prepared sequences: X shape (5429, 60, 39), y shape (5429,)
2025-06-13 19:58:13 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 19:58:13 | INFO     | ai_model:train:229 - Starting model training...
2025-06-13 19:58:22 | INFO     | ai_model:train:241 - Epoch 0: Train Loss: 0.4086, Train Acc: 0.8377, Val Loss: 0.3767, Val Acc: 0.8748
2025-06-13 19:59:46 | INFO     | ai_model:train:241 - Epoch 10: Train Loss: 0.3655, Train Acc: 0.8752, Val Loss: 0.3756, Val Acc: 0.8748
2025-06-13 19:59:46 | INFO     | ai_model:train:253 - Early stopping at epoch 10
2025-06-13 19:59:47 | INFO     | ai_model:train:282 - Model training completed successfully
2025-06-13 19:59:47 | INFO     | ai_model:train:283 - Validation Accuracy: 0.8748
2025-06-13 19:59:47 | INFO     | ai_model:train:284 - Validation F1-Score: 0.0000
2025-06-13 19:59:47 | INFO     | __main__:train_single_model:61 - Training completed in 94.43 seconds
2025-06-13 19:59:47 | INFO     | __main__:train_single_model:64 - Training Results:
2025-06-13 19:59:47 | INFO     | __main__:train_single_model:65 -   Validation Accuracy: 0.8748
2025-06-13 19:59:47 | INFO     | __main__:train_single_model:66 -   Validation Precision: 0.0000
2025-06-13 19:59:47 | INFO     | __main__:train_single_model:67 -   Validation Recall: 0.0000
2025-06-13 19:59:47 | INFO     | __main__:train_single_model:68 -   Validation F1-Score: 0.0000
2025-06-13 19:59:47 | INFO     | ai_model:save_model:386 - Model saved to models/xauusd_lstm_1h.h5
2025-06-13 19:59:47 | INFO     | __main__:train_single_model:74 - Model saved to models/xauusd_lstm_1h.h5
2025-06-13 19:59:47 | INFO     | __main__:main:287 - Training process completed successfully

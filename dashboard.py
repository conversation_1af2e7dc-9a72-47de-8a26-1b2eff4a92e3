"""
XAUUSD AI Trading System - Production Dashboard
A comprehensive monitoring dashboard for the AI trading system.
"""
from flask import Flask, render_template_string, jsonify
import psutil
import requests
import json
import os
from datetime import datetime
import sqlite3
from loguru import logger

app = Flask(__name__)

# Dashboard HTML template
DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XAUUSD AI Trading System Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
        }
        .metric-label {
            font-weight: 600;
            color: #2d3748;
        }
        .metric-value {
            font-weight: bold;
            color: #1a202c;
        }
        .status-good { color: #38a169; }
        .status-warning { color: #d69e2e; }
        .status-error { color: #e53e3e; }
        .json-display {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .refresh-info {
            text-align: center;
            color: white;
            margin-top: 20px;
            font-style: italic;
        }
        .timestamp {
            text-align: center;
            color: white;
            margin-bottom: 20px;
            font-size: 1.1em;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 5px;
        }
        .progress-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        .progress-low { background-color: #38a169; }
        .progress-medium { background-color: #d69e2e; }
        .progress-high { background-color: #e53e3e; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏆 XAUUSD AI Trading System</h1>
            <p>Production Monitoring Dashboard</p>
        </div>
        
        <div class="timestamp">
            <strong>Last Updated:</strong> {{ timestamp }}
        </div>
        
        <div class="status-grid">
            <!-- System Metrics Card -->
            <div class="card">
                <h3>📊 System Metrics</h3>
                <div class="metric">
                    <span class="metric-label">CPU Usage:</span>
                    <span class="metric-value {{ 'status-good' if cpu < 50 else 'status-warning' if cpu < 80 else 'status-error' }}">{{ cpu }}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill {{ 'progress-low' if cpu < 50 else 'progress-medium' if cpu < 80 else 'progress-high' }}" style="width: {{ cpu }}%"></div>
                </div>
                
                <div class="metric">
                    <span class="metric-label">Memory Usage:</span>
                    <span class="metric-value {{ 'status-good' if memory < 60 else 'status-warning' if memory < 85 else 'status-error' }}">{{ memory }}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill {{ 'progress-low' if memory < 60 else 'progress-medium' if memory < 85 else 'progress-high' }}" style="width: {{ memory }}%"></div>
                </div>
                
                <div class="metric">
                    <span class="metric-label">Disk Usage:</span>
                    <span class="metric-value {{ 'status-good' if disk < 70 else 'status-warning' if disk < 90 else 'status-error' }}">{{ disk }}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill {{ 'progress-low' if disk < 70 else 'progress-medium' if disk < 90 else 'progress-high' }}" style="width: {{ disk }}%"></div>
                </div>
            </div>
            
            <!-- AI System Status Card -->
            <div class="card">
                <h3>🤖 AI System Status</h3>
                <div class="metric">
                    <span class="metric-label">API Status:</span>
                    <span class="metric-value {{ 'status-good' if api_status == 'healthy' else 'status-error' }}">{{ api_status.upper() }}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Models Loaded:</span>
                    <span class="metric-value">{{ models_loaded }}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Signal Count:</span>
                    <span class="metric-value">{{ signal_count }}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Last Signal:</span>
                    <span class="metric-value">{{ last_signal_time }}</span>
                </div>
            </div>
            
            <!-- Service Status Card -->
            <div class="card">
                <h3>⚙️ Service Status</h3>
                <div class="metric">
                    <span class="metric-label">API Server:</span>
                    <span class="metric-value status-good">RUNNING</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Data Collector:</span>
                    <span class="metric-value {{ 'status-good' if data_collector_status == 'running' else 'status-warning' }}">{{ data_collector_status.upper() }}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Dashboard:</span>
                    <span class="metric-value status-good">RUNNING</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Database:</span>
                    <span class="metric-value status-good">CONNECTED</span>
                </div>
            </div>
        </div>
        
        <!-- Detailed API Data -->
        <div class="card">
            <h3>📈 Detailed System Information</h3>
            <div class="json-display">{{ api_data }}</div>
        </div>
        
        <div class="refresh-info">
            🔄 Dashboard auto-refreshes every 30 seconds
        </div>
    </div>
    
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function(){ 
            location.reload(); 
        }, 30000);
        
        // Add some interactivity
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-5px)';
                }, 200);
            });
        });
    </script>
</body>
</html>
'''

@app.route('/')
def dashboard():
    """Main dashboard route"""
    try:
        # System metrics
        cpu = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory().percent
        disk = psutil.disk_usage('/').percent
        
        # API status check
        api_status = "offline"
        api_data = {}
        models_loaded = 0
        signal_count = 0
        last_signal_time = "N/A"
        data_collector_status = "unknown"
        
        try:
            # Check API health
            health_response = requests.get('http://localhost:8000/health', timeout=5)
            if health_response.status_code == 200:
                api_status = "healthy"
                
                # Get detailed status
                status_response = requests.get('http://localhost:8000/status', timeout=5)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    api_data = status_data
                    models_loaded = status_data.get('models_loaded', 0)
                    signal_count = status_data.get('signal_count', 0)
                    
                    # Get last signal info
                    last_signals = status_data.get('last_signals', {})
                    if last_signals:
                        latest_signal = max(last_signals.values(), key=lambda x: x.get('timestamp', ''))
                        last_signal_time = latest_signal.get('timestamp', 'N/A')
                    
                    data_collector_status = "running" if status_data.get('is_running', False) else "stopped"
                    
        except requests.exceptions.RequestException:
            api_status = "offline"
            api_data = {"error": "API server not responding"}
        
        return render_template_string(
            DASHBOARD_TEMPLATE,
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC'),
            cpu=round(cpu, 1),
            memory=round(memory, 1),
            disk=round(disk, 1),
            api_status=api_status,
            models_loaded=models_loaded,
            signal_count=signal_count,
            last_signal_time=last_signal_time,
            data_collector_status=data_collector_status,
            api_data=json.dumps(api_data, indent=2)
        )
        
    except Exception as e:
        logger.error(f"Dashboard error: {e}")
        return f"Dashboard Error: {str(e)}", 500

@app.route('/api/metrics')
def api_metrics():
    """API endpoint for metrics (JSON)"""
    try:
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'system': {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent,
            },
            'api_status': 'unknown'
        }
        
        # Check API status
        try:
            response = requests.get('http://localhost:8000/health', timeout=3)
            metrics['api_status'] = 'healthy' if response.status_code == 200 else 'unhealthy'
        except:
            metrics['api_status'] = 'offline'
            
        return jsonify(metrics)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    logger.info("🚀 Starting XAUUSD AI Dashboard...")
    app.run(host='0.0.0.0', port=3000, debug=False)

# XAUUSD AI Trading System Configuration

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# Model Configuration
MODEL_PATH=models/xauusd_model.h5
MODEL_TYPE=lstm
SEQUENCE_LENGTH=60
PREDICTION_HORIZON=1

# Data Configuration
DATA_SOURCE=yfinance
SYMBOL=XAUUSD=X
LOOKBACK_DAYS=365

# Trading Configuration
SIGNAL_THRESHOLD=0.6
CONFIDENCE_THRESHOLD=0.7
RISK_MANAGEMENT=true
MAX_SIGNALS_PER_DAY=10

# TradingView Webhook Configuration
TRADINGVIEW_WEBHOOK_URL=https://your-tradingview-webhook-url.com/webhook
WEBHOOK_SECRET=your-secret-key-here

# Technical Indicators Configuration
RSI_PERIOD=14
MACD_FAST=12
MACD_SLOW=26
MACD_SIGNAL=9
BB_PERIOD=20
BB_STD=2.0
ADX_PERIOD=14

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/xauusd_ai.log

# Database Configuration (optional)
DATABASE_URL=sqlite:///xauusd_data.db

# External APIs (optional)
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-key
POLYGON_API_KEY=your-polygon-key

# Email Notifications (optional)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

# Telegram Notifications (optional)
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_CHAT_ID=your-telegram-chat-id

# Cloud Storage (optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_BUCKET_NAME=your-s3-bucket-name
AWS_REGION=us-east-1

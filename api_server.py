"""
FastAPI Server for XAUUSD AI Trading Signal API
"""
from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, List, Optional
import uvicorn
from datetime import datetime
import asyncio
from loguru import logger

from signal_generator import SignalGenerator
from data_collector import XAUUSDDataCollector
from ai_model import XAUUSDPredictor
from config import settings
from telegram_service import telegram_manager, send_trading_signal, send_system_alert, test_telegram_connection


# Pydantic models for API
class SignalRequest(BaseModel):
    timeframe: str = "1h"
    force_generate: bool = False


class SignalResponse(BaseModel):
    timeframe: str
    signal: str
    confidence: float
    probability: float
    current_price: float
    timestamp: str
    model_type: str
    market_context: Dict


class TrainingRequest(BaseModel):
    timeframe: str = "1h"
    model_type: str = "lstm"


class StatusResponse(BaseModel):
    is_running: bool
    last_signals: Dict
    signal_count: int
    models_loaded: int
    timeframes: List[str]
    uptime: str


# Global signal generator instance
signal_generator = None


# FastAPI app
app = FastAPI(
    title="XAUUSD AI Trading Signal API",
    description="Real-time AI-powered trading signals for XAUUSD",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
async def startup_event():
    """Initialize the signal generator on startup"""
    global signal_generator

    logger.info("Starting XAUUSD AI Trading Signal API...")

    try:
        signal_generator = SignalGenerator()

        # Initialize models
        if signal_generator.initialize_models():
            logger.info("API server started successfully")
        else:
            logger.error("Failed to initialize models")

        # Initialize Telegram service
        if telegram_manager.load_config():
            logger.info("Telegram service initialized")
            # Send startup notification
            await send_system_alert(
                "System Startup",
                "XAUUSD AI Trading System has started successfully",
                "success"
            )
        else:
            logger.info("Telegram service not configured (optional)")

    except Exception as e:
        logger.error(f"Error during startup: {e}")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global signal_generator
    
    if signal_generator:
        signal_generator.stop()
    
    logger.info("API server shutdown complete")


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "XAUUSD AI Trading Signal API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "models_loaded": len([p for p in signal_generator.predictors.values() if p.is_trained]) if signal_generator else 0
    }


@app.get("/status", response_model=StatusResponse)
async def get_status():
    """Get current status of the signal generator"""
    if not signal_generator:
        raise HTTPException(status_code=503, detail="Signal generator not initialized")
    
    status = signal_generator.get_status()
    status["uptime"] = "N/A"  # TODO: Calculate actual uptime
    
    return status


@app.post("/signal", response_model=SignalResponse)
async def generate_signal(request: SignalRequest, background_tasks: BackgroundTasks):
    """Generate trading signal for specified timeframe"""
    if not signal_generator:
        raise HTTPException(status_code=503, detail="Signal generator not initialized")

    try:
        # Validate timeframe
        if request.timeframe not in settings.TIMEFRAMES:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid timeframe. Supported: {settings.TIMEFRAMES}"
            )

        # Generate signal
        signal = signal_generator.generate_signal(request.timeframe)

        if not signal:
            # Return last known signal if no new signal generated
            last_signal = signal_generator.last_signals.get(request.timeframe)
            if last_signal and not request.force_generate:
                return SignalResponse(**last_signal)
            else:
                raise HTTPException(status_code=404, detail="No signal available")

        # Send Telegram notification for new signals
        if signal and telegram_manager.config_loaded:
            background_tasks.add_task(send_trading_signal, signal)

        return SignalResponse(**signal)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating signal: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/signals/all")
async def get_all_signals():
    """Get latest signals for all timeframes"""
    if not signal_generator:
        raise HTTPException(status_code=503, detail="Signal generator not initialized")
    
    try:
        signals = {}
        
        for timeframe in settings.TIMEFRAMES:
            signal = signal_generator.generate_signal(timeframe)
            if signal:
                signals[timeframe] = signal
            elif signal_generator.last_signals.get(timeframe):
                signals[timeframe] = signal_generator.last_signals[timeframe]
        
        return {
            "signals": signals,
            "timestamp": datetime.now().isoformat(),
            "count": len(signals)
        }
        
    except Exception as e:
        logger.error(f"Error getting all signals: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/signals/history")
async def get_signal_history(limit: int = 50):
    """Get signal history"""
    if not signal_generator:
        raise HTTPException(status_code=503, detail="Signal generator not initialized")
    
    try:
        history = signal_generator.signal_history[-limit:] if signal_generator.signal_history else []
        
        return {
            "history": history,
            "count": len(history),
            "total_signals": len(signal_generator.signal_history)
        }
        
    except Exception as e:
        logger.error(f"Error getting signal history: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/train")
async def train_model(request: TrainingRequest, background_tasks: BackgroundTasks):
    """Train AI model for specified timeframe"""
    try:
        # Validate inputs
        if request.timeframe not in settings.TIMEFRAMES:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid timeframe. Supported: {settings.TIMEFRAMES}"
            )
        
        if request.model_type not in ["lstm", "transformer"]:
            raise HTTPException(
                status_code=400, 
                detail="Invalid model type. Supported: lstm, transformer"
            )
        
        # Add training task to background
        background_tasks.add_task(
            train_model_background, 
            request.timeframe, 
            request.model_type
        )
        
        return {
            "message": f"Model training started for {request.timeframe} timeframe",
            "timeframe": request.timeframe,
            "model_type": request.model_type,
            "status": "training_started"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting model training: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def train_model_background(timeframe: str, model_type: str):
    """Background task for model training"""
    try:
        logger.info(f"Starting background training for {timeframe} with {model_type}")
        
        # Collect data
        collector = XAUUSDDataCollector()
        data = collector.get_processed_data(timeframe=timeframe)
        
        if data.empty:
            logger.error(f"No data available for training {timeframe}")
            return
        
        # Train model
        predictor = XAUUSDPredictor(model_type=model_type)
        metrics = predictor.train(data)
        
        if metrics and signal_generator:
            # Update the signal generator's predictor
            signal_generator.predictors[timeframe] = predictor
            logger.info(f"Model training completed for {timeframe}: {metrics}")
        else:
            logger.error(f"Model training failed for {timeframe}")
            
    except Exception as e:
        logger.error(f"Error in background training: {e}")


@app.post("/webhook/tradingview")
async def tradingview_webhook(payload: dict):
    """Receive webhook from TradingView"""
    try:
        logger.info(f"Received TradingView webhook: {payload}")

        # For TradingView webhooks, the secret validation is optional
        # TradingView sends data in different formats, so we'll be more flexible

        # Process webhook data
        return {
            "status": "received",
            "timestamp": datetime.now().isoformat(),
            "payload": payload,
            "message": "Webhook processed successfully"
        }

    except Exception as e:
        logger.error(f"Error processing webhook: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Add API prefix routes for Nginx compatibility
@app.post("/api/webhook/tradingview")
async def api_tradingview_webhook(payload: dict):
    """Handle TradingView webhook signals via /api/ prefix"""
    return await tradingview_webhook(payload)


@app.get("/api/health")
async def api_health_check():
    """Health check endpoint via /api/ prefix"""
    return await health_check()


@app.get("/api/status")
async def api_status():
    """Status endpoint via /api/ prefix"""
    return await get_status()


@app.post("/api/signal")
async def api_generate_signal(request: SignalRequest, background_tasks: BackgroundTasks):
    """Generate signal endpoint via /api/ prefix"""
    return await generate_signal(request, background_tasks)


@app.get("/api/data/{timeframe}")
async def api_get_market_data(timeframe: str, limit: int = 100):
    """Get market data endpoint via /api/ prefix"""
    return await get_market_data(timeframe, limit)


@app.post("/api/telegram/test")
async def api_test_telegram():
    """Test Telegram endpoint via /api/ prefix"""
    return await test_telegram()


@app.post("/api/telegram/send-test-signal")
async def api_send_test_telegram_signal():
    """Send test Telegram signal endpoint via /api/ prefix"""
    return await send_test_telegram_signal()


@app.get("/data/{timeframe}")
async def get_market_data(timeframe: str, limit: int = 100):
    """Get latest market data for specified timeframe"""
    try:
        if timeframe not in settings.TIMEFRAMES:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid timeframe. Supported: {settings.TIMEFRAMES}"
            )

        collector = XAUUSDDataCollector()
        data = collector.get_processed_data(timeframe=timeframe)

        if data.empty:
            raise HTTPException(status_code=404, detail="No data available")

        # Return latest data points
        latest_data = data.tail(limit)

        return {
            "timeframe": timeframe,
            "data": latest_data.to_dict('records'),
            "count": len(latest_data),
            "latest_price": float(latest_data['close'].iloc[-1]),
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting market data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/telegram/test")
async def test_telegram():
    """Test Telegram connection"""
    try:
        if not telegram_manager.config_loaded:
            if not telegram_manager.load_config():
                raise HTTPException(
                    status_code=503,
                    detail="Telegram not configured. Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID environment variables."
                )

        # Test connection
        success = await test_telegram_connection()

        if success:
            return {
                "status": "success",
                "message": "Telegram connection test successful",
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(
                status_code=503,
                detail="Telegram connection test failed"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing Telegram connection: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/telegram/send-test-signal")
async def send_test_telegram_signal():
    """Send a test trading signal to Telegram"""
    try:
        if not telegram_manager.config_loaded:
            raise HTTPException(
                status_code=503,
                detail="Telegram not configured"
            )

        # Create test signal
        test_signal = {
            "signal": "BUY",
            "confidence": 0.857,
            "current_price": 3457.60,
            "timeframe": "1h",
            "timestamp": datetime.now().isoformat(),
            "model_type": "Enhanced LSTM",
            "market_context": {
                "trend": "bullish",
                "volatility": "normal",
                "rsi_level": "oversold"
            }
        }

        # Send test signal
        success = await send_trading_signal(test_signal)

        if success:
            return {
                "status": "success",
                "message": "Test signal sent to Telegram",
                "signal": test_signal,
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(
                status_code=503,
                detail="Failed to send test signal to Telegram"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending test signal: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    # Create logs directory
    import os
    os.makedirs("logs", exist_ok=True)
    
    # Configure logging
    logger.add(
        settings.LOG_FILE,
        rotation="1 day",
        retention="30 days",
        level=settings.LOG_LEVEL
    )
    
    # Run the API server
    uvicorn.run(
        "api_server:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        workers=settings.API_WORKERS,
        reload=False
    )

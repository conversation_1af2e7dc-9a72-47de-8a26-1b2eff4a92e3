#!/usr/bin/env python3
"""
Quick Fix Script for XAUUSD AI Trading System Deployment Issues
This script fixes the common deployment problems identified in the error logs.
"""
import os
import sys
import subprocess
import time
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(cmd, description=""):
    """Run a command and return success status"""
    try:
        logger.info(f"🔧 {description}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"✅ {description} - Success")
            return True
        else:
            logger.error(f"❌ {description} - Failed")
            logger.error(f"Error: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"❌ {description} - Exception: {e}")
        return False

def fix_dependencies():
    """Fix missing dependencies"""
    logger.info("📦 Fixing dependencies...")
    
    commands = [
        ("pip install --upgrade pip", "Upgrading pip"),
        ("pip install pydantic-settings>=2.2.0", "Installing pydantic-settings"),
        ("pip install -r requirements.txt", "Installing all requirements"),
    ]
    
    for cmd, desc in commands:
        if not run_command(cmd, desc):
            return False
    
    return True

def test_imports():
    """Test critical imports"""
    logger.info("🧪 Testing imports...")
    
    test_modules = [
        "pydantic_settings",
        "config",
        "fastapi",
        "uvicorn",
        "gunicorn"
    ]
    
    for module in test_modules:
        try:
            __import__(module)
            logger.info(f"✅ {module} - OK")
        except ImportError as e:
            logger.error(f"❌ {module} - Failed: {e}")
            return False
    
    return True

def stop_existing_services():
    """Stop existing services that might be conflicting"""
    logger.info("🛑 Stopping existing services...")
    
    commands = [
        ("pkill -f gunicorn", "Stopping existing Gunicorn processes"),
        ("pkill -f uvicorn", "Stopping existing Uvicorn processes"),
        ("sudo systemctl stop xauusd-ai-api || true", "Stopping systemd service"),
        ("sudo systemctl stop xauusd-ai-dashboard || true", "Stopping dashboard service"),
        ("sudo systemctl stop xauusd-ai-data-collector || true", "Stopping data collector service"),
        ("sudo systemctl stop xauusd-ai-trainer || true", "Stopping trainer service"),
    ]
    
    for cmd, desc in commands:
        run_command(cmd, desc)  # Don't fail if these don't work
    
    time.sleep(2)
    logger.info("✅ Services stopped")

def create_directories():
    """Create required directories"""
    logger.info("📁 Creating required directories...")
    
    directories = ["logs", "models", "data"]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"✅ Created/verified directory: {directory}")

def test_application():
    """Test if the application can start"""
    logger.info("🧪 Testing application startup...")
    
    # Test with Python directly first
    test_cmd = """
import sys
sys.path.insert(0, '.')
try:
    from config import settings
    from api_server import app
    print("✅ Application imports successful")
except Exception as e:
    print(f"❌ Application import failed: {e}")
    sys.exit(1)
"""
    
    try:
        result = subprocess.run([sys.executable, "-c", test_cmd], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            logger.info("✅ Application test passed")
            return True
        else:
            logger.error(f"❌ Application test failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        logger.error("❌ Application test timed out")
        return False
    except Exception as e:
        logger.error(f"❌ Application test error: {e}")
        return False

def start_with_gunicorn():
    """Start the application with Gunicorn"""
    logger.info("🚀 Starting application with Gunicorn...")
    
    # Simple Gunicorn command
    cmd = [
        "gunicorn",
        "--bind", "0.0.0.0:8000",
        "--workers", "2",
        "--worker-class", "uvicorn.workers.UvicornWorker",
        "--timeout", "120",
        "--access-logfile", "logs/gunicorn_access.log",
        "--error-logfile", "logs/gunicorn_error.log",
        "--log-level", "info",
        "wsgi:application"
    ]
    
    try:
        logger.info(f"Running: {' '.join(cmd)}")
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for startup
        time.sleep(5)
        
        # Check if process is still running
        if process.poll() is None:
            logger.info(f"✅ Gunicorn started successfully with PID: {process.pid}")
            return process
        else:
            stdout, stderr = process.communicate()
            logger.error("❌ Gunicorn failed to start")
            logger.error(f"STDOUT: {stdout.decode()}")
            logger.error(f"STDERR: {stderr.decode()}")
            return None
            
    except Exception as e:
        logger.error(f"❌ Error starting Gunicorn: {e}")
        return None

def health_check():
    """Perform health check"""
    logger.info("🏥 Performing health check...")
    
    import requests
    
    for attempt in range(5):
        try:
            response = requests.get("http://localhost:8000/health", timeout=10)
            if response.status_code == 200:
                logger.info("✅ Health check passed")
                return True
            else:
                logger.warning(f"⚠️ Health check returned status: {response.status_code}")
        except requests.exceptions.RequestException as e:
            logger.warning(f"⚠️ Health check attempt {attempt + 1} failed: {e}")
        
        if attempt < 4:
            time.sleep(2)
    
    logger.error("❌ Health check failed after 5 attempts")
    return False

def main():
    """Main fix process"""
    logger.info("🔧 XAUUSD AI Trading System - Deployment Fix")
    logger.info("=" * 50)
    
    # Step 1: Stop existing services
    stop_existing_services()
    
    # Step 2: Create directories
    create_directories()
    
    # Step 3: Fix dependencies
    if not fix_dependencies():
        logger.error("❌ Failed to fix dependencies")
        return False
    
    # Step 4: Test imports
    if not test_imports():
        logger.error("❌ Import test failed")
        return False
    
    # Step 5: Test application
    if not test_application():
        logger.error("❌ Application test failed")
        return False
    
    # Step 6: Start with Gunicorn
    process = start_with_gunicorn()
    if not process:
        logger.error("❌ Failed to start Gunicorn")
        return False
    
    # Step 7: Health check
    if not health_check():
        logger.error("❌ Health check failed")
        if process:
            process.terminate()
        return False
    
    logger.info("🎉 XAUUSD AI Trading System fixed and running!")
    logger.info("🌐 Application: http://localhost:8000")
    logger.info("📊 API Docs: http://localhost:8000/docs")
    logger.info("🏥 Health: http://localhost:8000/health")
    
    # Keep running
    try:
        logger.info("⏳ System running... Press Ctrl+C to stop")
        while True:
            time.sleep(30)
            if not health_check():
                logger.error("❌ System health check failed!")
                break
    except KeyboardInterrupt:
        logger.info("🛑 Stopping system...")
        if process:
            process.terminate()
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

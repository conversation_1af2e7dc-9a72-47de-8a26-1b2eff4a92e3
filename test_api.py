"""
Test script for XAUUSD AI Trading API
"""
import requests
import json
from datetime import datetime

def test_api():
    """Test the API endpoints"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing XAUUSD AI Trading API")
    print("=" * 50)
    
    # Test health endpoint
    try:
        response = requests.get(f"{base_url}/health")
        print(f"✅ Health Check: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"❌ Health Check Failed: {e}")
        return
    
    # Test status endpoint
    try:
        response = requests.get(f"{base_url}/status")
        print(f"✅ Status Check: {response.status_code}")
        status = response.json()
        print(f"   Models Loaded: {status.get('models_loaded', 0)}")
        print(f"   Timeframes: {status.get('timeframes', [])}")
    except Exception as e:
        print(f"❌ Status Check Failed: {e}")
    
    # Test signal generation
    try:
        payload = {"timeframe": "1h", "force_generate": True}
        response = requests.post(f"{base_url}/signal", json=payload)
        print(f"✅ Signal Generation: {response.status_code}")
        
        if response.status_code == 200:
            signal = response.json()
            print(f"   Signal: {signal.get('signal', 'N/A')}")
            print(f"   Confidence: {signal.get('confidence', 0):.2%}")
            print(f"   Price: ${signal.get('current_price', 0):.2f}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"❌ Signal Generation Failed: {e}")
    
    # Test market data
    try:
        response = requests.get(f"{base_url}/data/1h?limit=5")
        print(f"✅ Market Data: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Records: {data.get('count', 0)}")
            print(f"   Latest Price: ${data.get('latest_price', 0):.2f}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"❌ Market Data Failed: {e}")
    
    print("\n🎉 API Testing Complete!")

if __name__ == "__main__":
    test_api()

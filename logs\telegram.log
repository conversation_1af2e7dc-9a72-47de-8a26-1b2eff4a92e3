2025-06-13 20:41:51.009 | INFO     | __main__:main:310 - 🧪 Testing Telegram Notification Service
2025-06-13 20:41:51.009 | WARNING  | __main__:load_config:236 - ⚠️ Telegram configuration not found in environment variables
2025-06-13 20:41:51.009 | INFO     | __main__:load_config:237 - Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID
2025-06-13 20:41:51.009 | ERROR    | __main__:main:314 - ❌ Failed to setup Telegram service
2025-06-13 21:15:26.084 | INFO     | api_server:startup_event:77 - Starting XAUUSD AI Trading Signal API...
2025-06-13 21:15:26.084 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:15:26.084 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:15:26.085 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:15:26.086 | INFO     | signal_generator:initialize_models:38 - Initializing AI models...
2025-06-13 21:15:26.086 | INFO     | signal_generator:initialize_models:41 - Loading model for 15m timeframe
2025-06-13 21:15:26.088 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:15:26.091 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:15:26.116 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:15:26.119 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:15:26.119 | INFO     | signal_generator:initialize_models:41 - Loading model for 1h timeframe
2025-06-13 21:15:26.119 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:15:26.121 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:15:26.124 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:15:26.124 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:15:26.125 | INFO     | signal_generator:initialize_models:41 - Loading model for 4h timeframe
2025-06-13 21:15:26.125 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:15:26.127 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:15:26.129 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:15:26.130 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:15:26.130 | INFO     | signal_generator:initialize_models:44 - All models initialized successfully
2025-06-13 21:15:26.130 | INFO     | api_server:startup_event:84 - API server started successfully
2025-06-13 21:15:26.131 | WARNING  | telegram_service:load_config:236 - ⚠️ Telegram configuration not found in environment variables
2025-06-13 21:15:26.131 | INFO     | telegram_service:load_config:237 - Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID
2025-06-13 21:15:26.131 | INFO     | api_server:startup_event:98 - Telegram service not configured (optional)
2025-06-13 21:16:13.568 | INFO     | api_server:startup_event:77 - Starting XAUUSD AI Trading Signal API...
2025-06-13 21:16:13.568 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:16:13.569 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:16:13.569 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:16:13.569 | INFO     | signal_generator:initialize_models:38 - Initializing AI models...
2025-06-13 21:16:13.569 | INFO     | signal_generator:initialize_models:41 - Loading model for 15m timeframe
2025-06-13 21:16:13.569 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:16:13.572 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:16:13.578 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:16:13.578 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:16:13.579 | INFO     | signal_generator:initialize_models:41 - Loading model for 1h timeframe
2025-06-13 21:16:13.579 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:16:13.581 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:16:13.584 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:16:13.584 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:16:13.584 | INFO     | signal_generator:initialize_models:41 - Loading model for 4h timeframe
2025-06-13 21:16:13.585 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:16:13.586 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:16:13.589 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:16:13.589 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:16:13.589 | INFO     | signal_generator:initialize_models:44 - All models initialized successfully
2025-06-13 21:16:13.590 | INFO     | api_server:startup_event:84 - API server started successfully
2025-06-13 21:16:13.590 | WARNING  | telegram_service:load_config:236 - ⚠️ Telegram configuration not found in environment variables
2025-06-13 21:16:13.590 | INFO     | telegram_service:load_config:237 - Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID
2025-06-13 21:16:13.590 | INFO     | api_server:startup_event:98 - Telegram service not configured (optional)
2025-06-13 21:16:44.620 | INFO     | signal_generator:generate_signal:54 - Generating signal for 1h timeframe
2025-06-13 21:16:46.557 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 21:16:46.679 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 21:16:46.687 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 21:16:46.692 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 21:16:46.699 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 21:16:46.700 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
2025-06-13 21:16:46.720 | INFO     | ai_model:predict:342 - Generated signal: SELL (confidence: 0.857)
2025-06-13 21:16:46.721 | ERROR    | signal_generator:_get_market_context:125 - Error getting market context: 'numpy.float64' object has no attribute 'rolling'
2025-06-13 21:16:46.722 | INFO     | signal_generator:generate_signal:98 - New 1h signal: SELL (confidence: 0.857)
2025-06-13 21:16:48.998 | INFO     | data_collector:fetch_yfinance_data:53 - Fetched 5739 records for 1h timeframe
2025-06-13 21:16:49.122 | INFO     | data_collector:calculate_technical_indicators:126 - Technical indicators calculated successfully
2025-06-13 21:16:49.132 | INFO     | data_collector:create_derived_features:169 - Derived features created successfully
2025-06-13 21:16:49.137 | INFO     | data_collector:create_labels:201 - Labels created successfully
2025-06-13 21:16:49.143 | INFO     | data_collector:preprocess_data:228 - Data preprocessing completed. Final shape: (5489, 43)
2025-06-13 21:16:49.144 | INFO     | data_collector:get_processed_data:248 - Successfully processed 5489 records for 1h
2025-06-13 21:31:04.895 | INFO     | api_server:startup_event:77 - Starting XAUUSD AI Trading Signal API...
2025-06-13 21:31:04.896 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:31:04.896 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:31:04.897 | INFO     | ai_model:__init__:94 - Using device: cpu
2025-06-13 21:31:04.897 | INFO     | signal_generator:initialize_models:38 - Initializing AI models...
2025-06-13 21:31:04.897 | INFO     | signal_generator:initialize_models:41 - Loading model for 15m timeframe
2025-06-13 21:31:04.897 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:31:04.900 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:31:04.906 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:31:04.907 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:31:04.907 | INFO     | signal_generator:initialize_models:41 - Loading model for 1h timeframe
2025-06-13 21:31:04.908 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:31:04.909 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:31:04.912 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:31:04.913 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:31:04.913 | INFO     | signal_generator:initialize_models:41 - Loading model for 4h timeframe
2025-06-13 21:31:04.913 | INFO     | ai_model:load_model:357 - Loaded feature info: 39 features
2025-06-13 21:31:04.915 | INFO     | ai_model:build_model:137 - LSTM model built successfully
2025-06-13 21:31:04.922 | INFO     | ai_model:load_model:367 - Model loaded successfully
2025-06-13 21:31:04.923 | INFO     | ai_model:load_model:371 - Scaler loaded successfully
2025-06-13 21:31:04.923 | INFO     | signal_generator:initialize_models:44 - All models initialized successfully
2025-06-13 21:31:04.923 | INFO     | api_server:startup_event:84 - API server started successfully
2025-06-13 21:31:04.923 | WARNING  | telegram_service:load_config:236 - ⚠️ Telegram configuration not found in environment variables
2025-06-13 21:31:04.923 | INFO     | telegram_service:load_config:237 - Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID
2025-06-13 21:31:04.923 | INFO     | api_server:startup_event:98 - Telegram service not configured (optional)
2025-06-13 21:39:03.172 | INFO     | signal_generator:stop:281 - Signal generator stopped
2025-06-13 21:39:03.174 | INFO     | api_server:shutdown_event:112 - API server shutdown complete

#!/bin/bash

# ============================================================================
# XAUUSD AI Trading System - Server Access Fix Script
# Fixes external API access issues for greateatfood.com
# ============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root for system operations
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_warning "Running as root. This is fine for system configuration."
    else
        print_status "Running as regular user. Will use sudo for system operations."
    fi
}

# Fix 1: Update API server to bind to all interfaces
fix_api_binding() {
    print_status "🔧 Fixing API server binding..."
    
    # Check if config.py exists and update API_HOST
    if [[ -f "config.py" ]]; then
        # Backup original
        cp config.py config.py.backup
        
        # Update API_HOST to bind to all interfaces
        sed -i 's/API_HOST: str = "127.0.0.1"/API_HOST: str = "0.0.0.0"/' config.py
        sed -i 's/API_HOST: str = "localhost"/API_HOST: str = "0.0.0.0"/' config.py
        
        print_success "API binding updated to 0.0.0.0"
    else
        print_warning "config.py not found in current directory"
    fi
}

# Fix 2: Update Nginx configuration
fix_nginx_config() {
    print_status "🌐 Fixing Nginx configuration..."
    
    # Create proper nginx configuration
    sudo tee /etc/nginx/sites-available/xauusd-ai << 'EOF'
server {
    listen 80;
    server_name greateatfood.com www.greateatfood.com;

    # API endpoints - Remove trailing slash from proxy_pass
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Add CORS headers
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;
    }

    # Dashboard
    location /dashboard/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Root redirect to dashboard
    location / {
        return 301 /dashboard/;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://127.0.0.1:8000/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
EOF

    # Enable the site
    sudo ln -sf /etc/nginx/sites-available/xauusd-ai /etc/nginx/sites-enabled/
    
    # Remove default site if it exists
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Test nginx configuration
    if sudo nginx -t; then
        print_success "Nginx configuration is valid"
        sudo systemctl reload nginx
        print_success "Nginx reloaded"
    else
        print_error "Nginx configuration test failed"
        return 1
    fi
}

# Fix 3: Configure firewall
fix_firewall() {
    print_status "🔥 Configuring firewall..."
    
    # Check if ufw is installed and active
    if command -v ufw >/dev/null 2>&1; then
        # Allow necessary ports
        sudo ufw allow 22/tcp    # SSH
        sudo ufw allow 80/tcp    # HTTP
        sudo ufw allow 443/tcp   # HTTPS
        sudo ufw allow 8000/tcp  # API server
        sudo ufw allow 3000/tcp  # Dashboard
        
        # Enable firewall if not already enabled
        sudo ufw --force enable
        
        print_success "Firewall configured"
    else
        print_warning "UFW not installed, skipping firewall configuration"
    fi
}

# Fix 4: Restart services in correct order
restart_services() {
    print_status "🔄 Restarting services..."
    
    # Stop existing processes
    pkill -f "python.*api_server" || true
    pkill -f "python.*dashboard" || true
    pkill -f "gunicorn" || true
    pkill -f "uvicorn" || true
    
    sleep 3
    
    # Start API server
    print_status "Starting API server..."
    cd /home/<USER>/xauusd-ai-system
    source venv/bin/activate
    
    # Start API server in background
    nohup python api_server.py > logs/api_server.log 2>&1 &
    API_PID=$!
    
    sleep 5
    
    # Check if API server started
    if kill -0 $API_PID 2>/dev/null; then
        print_success "API server started (PID: $API_PID)"
    else
        print_error "API server failed to start"
        return 1
    fi
    
    # Start dashboard
    print_status "Starting dashboard..."
    nohup python dashboard.py > logs/dashboard.log 2>&1 &
    DASHBOARD_PID=$!
    
    sleep 3
    
    # Check if dashboard started
    if kill -0 $DASHBOARD_PID 2>/dev/null; then
        print_success "Dashboard started (PID: $DASHBOARD_PID)"
    else
        print_error "Dashboard failed to start"
        return 1
    fi
    
    # Restart nginx
    sudo systemctl restart nginx
    print_success "Nginx restarted"
}

# Fix 5: Test connectivity
test_connectivity() {
    print_status "🧪 Testing connectivity..."
    
    # Wait for services to fully start
    sleep 10
    
    # Test local API
    if curl -s http://localhost:8000/health | grep -q "healthy"; then
        print_success "Local API responding"
    else
        print_error "Local API not responding"
        return 1
    fi
    
    # Test local dashboard
    if curl -s http://localhost:3000 | grep -q "XAUUSD"; then
        print_success "Local dashboard responding"
    else
        print_warning "Local dashboard may not be responding"
    fi
    
    # Test external access
    print_status "Testing external access..."
    if curl -s http://greateatfood.com/health | grep -q "healthy"; then
        print_success "External API access working!"
    else
        print_warning "External API access test failed - may need DNS propagation time"
    fi
}

# Main execution
main() {
    print_status "🚀 Starting server access fix..."
    
    check_root
    
    # Execute fixes
    fix_api_binding
    fix_nginx_config
    fix_firewall
    restart_services
    test_connectivity
    
    print_success "🎉 Server access fix completed!"
    print_status "📍 Access points:"
    print_status "   - API Health: http://greateatfood.com/health"
    print_status "   - API Webhook: http://greateatfood.com/api/webhook/tradingview"
    print_status "   - Dashboard: http://greateatfood.com/dashboard/"
    print_status "   - Direct API: http://greateatfood.com:8000/health"
    
    print_status "📋 Next steps:"
    print_status "   1. Update TradingView webhook URL to: https://greateatfood.com/api/webhook/tradingview"
    print_status "   2. Test the Pine Script indicator"
    print_status "   3. Monitor logs: tail -f logs/api_server.log"
}

# Run main function
main "$@"

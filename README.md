# XAUUSD AI Trading Signal System

A sophisticated AI-powered trading signal system for XAUUSD (Gold vs USD) that integrates with TradingView through external webhooks and APIs.

## 🚀 Features

- **Advanced AI Models**: LSTM and Transformer neural networks for signal prediction
- **Multi-Timeframe Analysis**: Optimized for 15m, 1h, and 4h timeframes
- **Real-time Signal Generation**: Continuous monitoring and signal generation
- **TradingView Integration**: Seamless webhook integration with TradingView
- **RESTful API**: Complete API for signal access and model management
- **Technical Analysis**: 20+ technical indicators for enhanced prediction
- **Risk Management**: Configurable confidence thresholds and signal filtering
- **Docker Support**: Easy deployment with Docker and Docker Compose
- **Comprehensive Logging**: Detailed logging and monitoring capabilities

## 📋 Table of Contents

- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [API Documentation](#api-documentation)
- [TradingView Setup](#tradingview-setup)
- [Model Training](#model-training)
- [Deployment](#deployment)
- [Monitoring](#monitoring)
- [Troubleshooting](#troubleshooting)

## 🛠 Installation

### Prerequisites

- Python 3.11+
- Docker (optional but recommended)
- TradingView account with webhook capabilities

### Local Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd TRVBOT
```

2. **Create virtual environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
```

4. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. **Create necessary directories**
```bash
mkdir -p models logs data
```

### Docker Installation

1. **Clone and configure**
```bash
git clone <repository-url>
cd TRVBOT
cp .env.example .env
# Edit .env with your configuration
```

2. **Build and run with Docker Compose**
```bash
docker-compose up -d
```

## ⚙️ Configuration

### Environment Variables

Key configuration options in `.env`:

```env
# Model Configuration
MODEL_TYPE=lstm                    # lstm or transformer
SIGNAL_THRESHOLD=0.6              # Minimum signal threshold
CONFIDENCE_THRESHOLD=0.7          # Minimum confidence level

# TradingView Integration
TRADINGVIEW_WEBHOOK_URL=your-webhook-url
WEBHOOK_SECRET=your-secret-key

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
```

### Timeframe-Specific Parameters

The system automatically adjusts parameters for different timeframes:

- **15m**: Higher sensitivity for scalping
- **1h**: Balanced approach for day trading
- **4h**: Lower sensitivity for swing trading

## 🚀 Usage

### Starting the System

**Local Development:**
```bash
python api_server.py
```

**Production (Docker):**
```bash
docker-compose up -d
```

### API Endpoints

The system exposes several REST API endpoints:

- `GET /health` - Health check
- `POST /signal` - Generate trading signal
- `GET /signals/all` - Get all timeframe signals
- `GET /signals/history` - Get signal history
- `POST /train` - Train AI model
- `GET /data/{timeframe}` - Get market data

### Basic Usage Example

```python
import requests

# Get trading signal
response = requests.post('http://localhost:8000/signal', 
                        json={'timeframe': '1h'})
signal = response.json()

print(f"Signal: {signal['signal']}")
print(f"Confidence: {signal['confidence']:.2%}")
```

## 📊 API Documentation

### Generate Signal

**Endpoint:** `POST /signal`

**Request:**
```json
{
    "timeframe": "1h",
    "force_generate": false
}
```

**Response:**
```json
{
    "timeframe": "1h",
    "signal": "BUY",
    "confidence": 0.75,
    "probability": 0.82,
    "current_price": 2045.50,
    "timestamp": "2024-01-15T10:30:00",
    "model_type": "lstm",
    "market_context": {
        "trend": "bullish",
        "volatility": "normal",
        "rsi_level": "neutral"
    }
}
```

### Get All Signals

**Endpoint:** `GET /signals/all`

**Response:**
```json
{
    "signals": {
        "15m": {...},
        "1h": {...},
        "4h": {...}
    },
    "timestamp": "2024-01-15T10:30:00",
    "count": 3
}
```

## 📈 TradingView Setup

### 1. Add the Pine Script Indicator

1. Open TradingView and go to Pine Editor
2. Copy the content from `xauusd_ai_indicator.pine`
3. Save and add to chart

### 2. Configure Webhook Integration

1. **Set up webhook endpoint** in your deployment
2. **Configure TradingView alerts** to send data to your API
3. **Set up return webhooks** from your API to TradingView

### 3. Pine Script Configuration

The indicator includes several configurable parameters:

- **AI Configuration**: Enable/disable AI signals
- **Timeframes**: Select active timeframes
- **Display Settings**: Customize visual elements
- **Signal Filtering**: Set confidence thresholds
- **Alerts**: Configure alert preferences

### 4. Alert Setup

Create TradingView alerts that:
1. Trigger on specific market conditions
2. Send webhook to your AI system
3. Receive processed signals back
4. Display on chart with confidence levels

## 🧠 Model Training

### Automatic Training

The system automatically retrains models daily at 2:00 AM:

```python
# Scheduled in signal_generator.py
schedule.every().day.at("02:00").do(self.retrain_models)
```

### Manual Training

**Via API:**
```bash
curl -X POST "http://localhost:8000/train" \
     -H "Content-Type: application/json" \
     -d '{"timeframe": "1h", "model_type": "lstm"}'
```

**Via Python:**
```python
from ai_model import XAUUSDPredictor
from data_collector import XAUUSDDataCollector

# Collect data
collector = XAUUSDDataCollector()
data = collector.get_processed_data(timeframe="1h")

# Train model
predictor = XAUUSDPredictor(model_type="lstm")
metrics = predictor.train(data)
```

### Model Performance Metrics

The training process provides comprehensive metrics:

- **Accuracy**: Overall prediction accuracy
- **Precision**: True positive rate
- **Recall**: Sensitivity to positive signals
- **F1-Score**: Harmonic mean of precision and recall

## 🚢 Deployment

### Local Development

```bash
# Start API server
python api_server.py

# Start signal generator (separate terminal)
python signal_generator.py
```

### Docker Deployment

```bash
# Build and start
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Cloud Deployment

**AWS ECS/Fargate:**
1. Build and push Docker image to ECR
2. Create ECS task definition
3. Deploy to Fargate cluster
4. Configure load balancer and auto-scaling

**Google Cloud Run:**
```bash
# Build and deploy
gcloud builds submit --tag gcr.io/PROJECT-ID/xauusd-ai
gcloud run deploy --image gcr.io/PROJECT-ID/xauusd-ai --platform managed
```

**Azure Container Instances:**
```bash
# Create resource group and deploy
az group create --name xauusd-ai --location eastus
az container create --resource-group xauusd-ai --name xauusd-ai-container \
  --image your-registry/xauusd-ai:latest --ports 8000
```

## 📊 Monitoring

### Health Checks

The system includes comprehensive health monitoring:

```bash
# Check API health
curl http://localhost:8000/health

# Check system status
curl http://localhost:8000/status
```

### Logging

Logs are written to:
- Console (development)
- `logs/xauusd_ai.log` (production)
- Docker logs (containerized deployment)

### Metrics

Key metrics to monitor:

- **Signal Generation Rate**: Signals per hour/day
- **Model Accuracy**: Prediction accuracy over time
- **API Response Time**: Latency of signal generation
- **Error Rate**: Failed predictions or API calls
- **Confidence Distribution**: Distribution of signal confidence levels

## 🔧 Troubleshooting

### Common Issues

**1. No Data Retrieved**
```bash
# Check data source connectivity
python -c "from data_collector import XAUUSDDataCollector; print(XAUUSDDataCollector().fetch_yfinance_data())"
```

**2. Model Training Fails**
- Ensure sufficient historical data (minimum 1000 records)
- Check memory availability for model training
- Verify all required features are present

**3. API Connection Issues**
- Verify port 8000 is not blocked
- Check firewall settings
- Ensure Docker containers are running

**4. TradingView Integration Issues**
- Verify webhook URLs are accessible
- Check webhook secret configuration
- Ensure TradingView account has webhook capabilities

### Debug Mode

Enable debug logging:

```env
LOG_LEVEL=DEBUG
```

### Performance Optimization

**For better performance:**

1. **Use GPU acceleration** for model training:
```bash
pip install tensorflow-gpu
```

2. **Optimize data collection frequency**:
```python
# Reduce data collection frequency for production
LOOKBACK_DAYS=180  # Instead of 365
```

3. **Use Redis for caching**:
```yaml
# Uncomment Redis service in docker-compose.yml
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the API documentation

## 🔄 Updates and Maintenance

The system includes automatic updates for:
- Daily model retraining
- Market data refresh
- Signal history cleanup
- Log rotation

For manual updates:
```bash
# Update dependencies
pip install -r requirements.txt --upgrade

# Rebuild Docker image
docker-compose build --no-cache
```

#!/usr/bin/env python3
"""
Production Startup Script for XAUUSD AI Trading System
This script handles the complete production deployment process.
"""
import os
import sys
import subprocess
import time
import signal
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/production_startup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionManager:
    def __init__(self):
        self.base_dir = Path(__file__).parent.absolute()
        self.venv_path = self.base_dir / "venv"
        self.python_path = self.venv_path / "bin" / "python" if os.name != 'nt' else self.venv_path / "Scripts" / "python.exe"
        self.gunicorn_pid = None
        
    def check_environment(self):
        """Check if the environment is properly set up"""
        logger.info("🔍 Checking production environment...")
        
        # Check if virtual environment exists
        if not self.venv_path.exists():
            logger.error("❌ Virtual environment not found. Please run setup first.")
            return False
            
        # Check if Python executable exists
        if not self.python_path.exists():
            logger.error("❌ Python executable not found in virtual environment.")
            return False
            
        # Check if required directories exist
        required_dirs = ['logs', 'models']
        for dir_name in required_dirs:
            dir_path = self.base_dir / dir_name
            if not dir_path.exists():
                logger.info(f"📁 Creating directory: {dir_name}")
                dir_path.mkdir(exist_ok=True)
                
        logger.info("✅ Environment check passed")
        return True
        
    def install_dependencies(self):
        """Install/update dependencies"""
        logger.info("📦 Installing/updating dependencies...")
        
        try:
            # Upgrade pip first
            subprocess.run([
                str(self.python_path), "-m", "pip", "install", "--upgrade", "pip"
            ], check=True, capture_output=True, text=True)
            
            # Install requirements
            subprocess.run([
                str(self.python_path), "-m", "pip", "install", "-r", "requirements.txt"
            ], check=True, capture_output=True, text=True)
            
            logger.info("✅ Dependencies installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to install dependencies: {e}")
            logger.error(f"Error output: {e.stderr}")
            return False
            
    def test_imports(self):
        """Test if all required modules can be imported"""
        logger.info("🧪 Testing module imports...")
        
        test_script = """
import sys
sys.path.insert(0, '.')

try:
    from config import settings
    from api_server import app
    from signal_generator import SignalGenerator
    from data_collector import XAUUSDDataCollector
    print("✅ All imports successful")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)
"""
        
        try:
            result = subprocess.run([
                str(self.python_path), "-c", test_script
            ], capture_output=True, text=True, cwd=str(self.base_dir))
            
            if result.returncode == 0:
                logger.info("✅ All module imports successful")
                return True
            else:
                logger.error(f"❌ Import test failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error running import test: {e}")
            return False
            
    def start_gunicorn(self):
        """Start the Gunicorn server"""
        logger.info("🚀 Starting Gunicorn server...")
        
        try:
            # Gunicorn command
            cmd = [
                str(self.python_path), "-m", "gunicorn",
                "--config", "gunicorn.conf.py",
                "wsgi:application"
            ]
            
            # Start Gunicorn
            process = subprocess.Popen(
                cmd,
                cwd=str(self.base_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            self.gunicorn_pid = process.pid
            logger.info(f"✅ Gunicorn started with PID: {self.gunicorn_pid}")
            
            # Wait a moment and check if it's still running
            time.sleep(3)
            if process.poll() is None:
                logger.info("✅ Gunicorn is running successfully")
                return process
            else:
                stdout, stderr = process.communicate()
                logger.error(f"❌ Gunicorn failed to start")
                logger.error(f"STDOUT: {stdout.decode()}")
                logger.error(f"STDERR: {stderr.decode()}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error starting Gunicorn: {e}")
            return None
            
    def health_check(self):
        """Perform health check on the running application"""
        logger.info("🏥 Performing health check...")
        
        import requests
        
        try:
            response = requests.get("http://localhost:8000/health", timeout=10)
            if response.status_code == 200:
                logger.info("✅ Health check passed")
                return True
            else:
                logger.error(f"❌ Health check failed with status: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Health check failed: {e}")
            return False
            
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"🛑 Received signal {signum}, shutting down...")
            if self.gunicorn_pid:
                try:
                    os.kill(self.gunicorn_pid, signal.SIGTERM)
                    logger.info("✅ Gunicorn shutdown signal sent")
                except ProcessLookupError:
                    logger.info("Gunicorn process already terminated")
            sys.exit(0)
            
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
    def run(self):
        """Main production startup process"""
        logger.info("🎯 Starting XAUUSD AI Trading System - Production Mode")
        logger.info("=" * 60)
        
        # Setup signal handlers
        self.setup_signal_handlers()
        
        # Check environment
        if not self.check_environment():
            logger.error("❌ Environment check failed")
            return False
            
        # Install dependencies
        if not self.install_dependencies():
            logger.error("❌ Dependency installation failed")
            return False
            
        # Test imports
        if not self.test_imports():
            logger.error("❌ Import test failed")
            return False
            
        # Start Gunicorn
        process = self.start_gunicorn()
        if not process:
            logger.error("❌ Failed to start Gunicorn")
            return False
            
        # Wait for startup
        logger.info("⏳ Waiting for application to start...")
        time.sleep(5)
        
        # Health check
        if not self.health_check():
            logger.error("❌ Health check failed")
            return False
            
        logger.info("🎉 XAUUSD AI Trading System started successfully!")
        logger.info("🌐 Application is running at: http://localhost:8000")
        logger.info("📊 API Documentation: http://localhost:8000/docs")
        logger.info("🏥 Health Check: http://localhost:8000/health")
        
        # Keep running
        try:
            logger.info("⏳ System running... Press Ctrl+C to stop")
            while True:
                time.sleep(60)
                # Periodic health check
                if not self.health_check():
                    logger.error("❌ Health check failed, system may be down!")
                    break
                    
        except KeyboardInterrupt:
            logger.info("🛑 Shutdown requested")
            
        return True

if __name__ == "__main__":
    manager = ProductionManager()
    success = manager.run()
    sys.exit(0 if success else 1)

"""
Training Script for XAUUSD AI Models
"""
import argparse
import os
import sys
from datetime import datetime
from loguru import logger

from data_collector import XAUUSDDataCollector
from ai_model import XAUUSDPredictor
from config import settings


def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    logger.remove()
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # Also log to file
    os.makedirs("logs", exist_ok=True)
    logger.add(
        "logs/training.log",
        level=log_level,
        rotation="1 day",
        retention="30 days",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
    )


def train_single_model(timeframe: str, model_type: str = "lstm", save_model: bool = True) -> dict:
    """Train a single model for specified timeframe"""
    try:
        logger.info(f"Starting training for {timeframe} timeframe with {model_type} model")
        
        # Collect data
        logger.info("Collecting and preprocessing data...")
        collector = XAUUSDDataCollector()
        data = collector.get_processed_data(timeframe=timeframe)
        
        if data.empty:
            logger.error(f"No data available for {timeframe} timeframe")
            return {}
        
        logger.info(f"Collected {len(data)} data points for training")
        
        # Initialize model
        predictor = XAUUSDPredictor(model_type=model_type)
        
        # Train model
        logger.info("Starting model training...")
        start_time = datetime.now()
        
        metrics = predictor.train(data)
        
        training_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"Training completed in {training_time:.2f} seconds")
        
        if metrics:
            logger.info("Training Results:")
            logger.info(f"  Validation Accuracy: {metrics.get('val_accuracy', 0):.4f}")
            logger.info(f"  Validation Precision: {metrics.get('val_precision', 0):.4f}")
            logger.info(f"  Validation Recall: {metrics.get('val_recall', 0):.4f}")
            logger.info(f"  Validation F1-Score: {metrics.get('val_f1', 0):.4f}")
            
            # Save model if requested
            if save_model:
                model_path = f"models/xauusd_{model_type}_{timeframe}.h5"
                predictor.save_model(model_path)
                logger.info(f"Model saved to {model_path}")
            
            # Add training metadata
            metrics.update({
                'timeframe': timeframe,
                'model_type': model_type,
                'training_time': training_time,
                'data_points': len(data),
                'timestamp': datetime.now().isoformat()
            })
            
            return metrics
        else:
            logger.error("Training failed - no metrics returned")
            return {}
            
    except Exception as e:
        logger.error(f"Error training model for {timeframe}: {e}")
        return {}


def train_all_models(model_type: str = "lstm", save_models: bool = True) -> dict:
    """Train models for all timeframes"""
    logger.info(f"Starting training for all timeframes with {model_type} model")
    
    results = {}
    
    for timeframe in settings.TIMEFRAMES:
        logger.info(f"\n{'='*50}")
        logger.info(f"Training {timeframe} model")
        logger.info(f"{'='*50}")
        
        metrics = train_single_model(timeframe, model_type, save_models)
        results[timeframe] = metrics
        
        if metrics:
            logger.info(f"✓ {timeframe} model training completed successfully")
        else:
            logger.error(f"✗ {timeframe} model training failed")
    
    return results


def evaluate_model(timeframe: str, model_type: str = "lstm") -> dict:
    """Evaluate a trained model"""
    try:
        logger.info(f"Evaluating {model_type} model for {timeframe} timeframe")
        
        # Load model
        predictor = XAUUSDPredictor(model_type=model_type)
        model_path = f"models/xauusd_{model_type}_{timeframe}.h5"
        
        if not os.path.exists(model_path):
            logger.error(f"Model file not found: {model_path}")
            return {}
        
        predictor.load_model()
        
        # Get test data
        collector = XAUUSDDataCollector()
        data = collector.get_processed_data(timeframe=timeframe)
        
        if data.empty:
            logger.error(f"No data available for evaluation")
            return {}
        
        # Use last 20% of data for evaluation
        test_size = int(len(data) * 0.2)
        test_data = data.tail(test_size)
        
        logger.info(f"Evaluating on {len(test_data)} test samples")
        
        # Generate predictions
        predictions = []
        actuals = []
        
        for i in range(settings.SEQUENCE_LENGTH, len(test_data)):
            # Get sequence for prediction
            sequence_data = test_data.iloc[i-settings.SEQUENCE_LENGTH:i]
            prediction = predictor.predict(sequence_data)
            
            if prediction:
                predictions.append(1 if prediction['signal'] == 'BUY' else 0)
                actuals.append(test_data.iloc[i]['binary_signal'])
        
        if predictions:
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
            
            accuracy = accuracy_score(actuals, predictions)
            precision = precision_score(actuals, predictions, zero_division=0)
            recall = recall_score(actuals, predictions, zero_division=0)
            f1 = f1_score(actuals, predictions, zero_division=0)
            cm = confusion_matrix(actuals, predictions)
            
            evaluation_results = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'confusion_matrix': cm.tolist(),
                'test_samples': len(predictions),
                'timeframe': timeframe,
                'model_type': model_type
            }
            
            logger.info("Evaluation Results:")
            logger.info(f"  Accuracy: {accuracy:.4f}")
            logger.info(f"  Precision: {precision:.4f}")
            logger.info(f"  Recall: {recall:.4f}")
            logger.info(f"  F1-Score: {f1:.4f}")
            logger.info(f"  Test Samples: {len(predictions)}")
            
            return evaluation_results
        else:
            logger.error("No predictions generated during evaluation")
            return {}
            
    except Exception as e:
        logger.error(f"Error evaluating model: {e}")
        return {}


def compare_models(timeframe: str) -> dict:
    """Compare LSTM vs Transformer models for a timeframe"""
    logger.info(f"Comparing models for {timeframe} timeframe")
    
    results = {}
    
    for model_type in ["lstm", "transformer"]:
        logger.info(f"\nTraining {model_type} model...")
        metrics = train_single_model(timeframe, model_type, save_model=True)
        
        if metrics:
            # Also evaluate the model
            eval_metrics = evaluate_model(timeframe, model_type)
            metrics.update(eval_metrics)
        
        results[model_type] = metrics
    
    # Compare results
    if results.get("lstm") and results.get("transformer"):
        logger.info(f"\nModel Comparison for {timeframe}:")
        logger.info(f"{'Metric':<15} {'LSTM':<10} {'Transformer':<12}")
        logger.info("-" * 40)
        
        for metric in ['val_accuracy', 'val_precision', 'val_recall', 'val_f1']:
            lstm_val = results["lstm"].get(metric, 0)
            transformer_val = results["transformer"].get(metric, 0)
            logger.info(f"{metric:<15} {lstm_val:<10.4f} {transformer_val:<12.4f}")
    
    return results


def main():
    """Main training function"""
    parser = argparse.ArgumentParser(description="Train XAUUSD AI Trading Models")
    parser.add_argument("--timeframe", type=str, choices=["15m", "1h", "4h", "all"], 
                       default="all", help="Timeframe to train")
    parser.add_argument("--model-type", type=str, choices=["lstm", "transformer", "both"], 
                       default="lstm", help="Model type to train")
    parser.add_argument("--evaluate", action="store_true", help="Evaluate trained models")
    parser.add_argument("--compare", action="store_true", help="Compare LSTM vs Transformer")
    parser.add_argument("--log-level", type=str, default="INFO", 
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="Log level")
    parser.add_argument("--no-save", action="store_true", help="Don't save trained models")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    logger.info("XAUUSD AI Model Training Started")
    logger.info(f"Configuration: timeframe={args.timeframe}, model_type={args.model_type}")
    
    try:
        if args.compare:
            # Compare models
            if args.timeframe == "all":
                for tf in settings.TIMEFRAMES:
                    compare_models(tf)
            else:
                compare_models(args.timeframe)
                
        elif args.evaluate:
            # Evaluate existing models
            if args.timeframe == "all":
                for tf in settings.TIMEFRAMES:
                    if args.model_type in ["lstm", "both"]:
                        evaluate_model(tf, "lstm")
                    if args.model_type in ["transformer", "both"]:
                        evaluate_model(tf, "transformer")
            else:
                if args.model_type in ["lstm", "both"]:
                    evaluate_model(args.timeframe, "lstm")
                if args.model_type in ["transformer", "both"]:
                    evaluate_model(args.timeframe, "transformer")
        else:
            # Train models
            save_models = not args.no_save
            
            if args.timeframe == "all":
                if args.model_type == "both":
                    for model_type in ["lstm", "transformer"]:
                        train_all_models(model_type, save_models)
                else:
                    train_all_models(args.model_type, save_models)
            else:
                if args.model_type == "both":
                    for model_type in ["lstm", "transformer"]:
                        train_single_model(args.timeframe, model_type, save_models)
                else:
                    train_single_model(args.timeframe, args.model_type, save_models)
        
        logger.info("Training process completed successfully")
        
    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

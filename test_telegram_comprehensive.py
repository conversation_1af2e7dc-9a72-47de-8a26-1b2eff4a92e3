#!/usr/bin/env python3
"""
Comprehensive Telegram Test Script for XAUUSD AI Trading System
Tests all aspects of Telegram integration including bot setup, notifications, and API endpoints.
"""

import os
import sys
import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional
from loguru import logger
import requests

# Setup logging
logger.remove()
logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>")

class TelegramTester:
    """Comprehensive Telegram testing class"""
    
    def __init__(self):
        self.bot_token = None
        self.chat_id = None
        self.base_url = None
        self.session = None
        self.test_results = {
            "bot_validation": False,
            "chat_validation": False,
            "basic_message": False,
            "trading_signal": False,
            "system_alert": False,
            "api_integration": False,
            "webhook_test": False
        }
    
    def print_header(self, title: str):
        """Print formatted header"""
        print(f"\n{'='*60}")
        print(f"🧪 {title}")
        print(f"{'='*60}")
    
    def print_result(self, test_name: str, success: bool, details: str = ""):
        """Print test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}")
        if details:
            print(f"    {details}")
    
    def load_config(self) -> bool:
        """Load Telegram configuration"""
        try:
            # Try to load from environment variables
            self.bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
            self.chat_id = os.getenv('TELEGRAM_CHAT_ID')
            
            if not self.bot_token or not self.chat_id:
                # Try to load from .env file
                if os.path.exists('.env'):
                    with open('.env', 'r') as f:
                        for line in f:
                            if line.startswith('TELEGRAM_BOT_TOKEN='):
                                self.bot_token = line.split('=', 1)[1].strip()
                            elif line.startswith('TELEGRAM_CHAT_ID='):
                                self.chat_id = line.split('=', 1)[1].strip()
            
            if not self.bot_token or not self.chat_id:
                logger.error("❌ Telegram configuration not found!")
                logger.info("Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID in environment or .env file")
                return False
            
            self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
            logger.info("✅ Telegram configuration loaded")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error loading configuration: {e}")
            return False
    
    async def test_bot_token(self) -> bool:
        """Test if bot token is valid"""
        try:
            url = f"{self.base_url}/getMe"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('ok'):
                            bot_info = data.get('result', {})
                            bot_name = bot_info.get('first_name', 'Unknown')
                            bot_username = bot_info.get('username', 'Unknown')
                            
                            self.print_result("Bot Token Validation", True, 
                                            f"Bot: {bot_name} (@{bot_username})")
                            self.test_results["bot_validation"] = True
                            return True
                    
                    error_data = await response.json()
                    self.print_result("Bot Token Validation", False, 
                                    f"Error: {error_data.get('description', 'Unknown error')}")
                    return False
                    
        except Exception as e:
            self.print_result("Bot Token Validation", False, f"Exception: {str(e)}")
            return False
    
    async def test_chat_id(self) -> bool:
        """Test if chat ID is valid"""
        try:
            url = f"{self.base_url}/getChat"
            params = {"chat_id": self.chat_id}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('ok'):
                            chat_info = data.get('result', {})
                            chat_type = chat_info.get('type', 'Unknown')
                            chat_title = chat_info.get('title') or chat_info.get('first_name', 'Unknown')
                            
                            self.print_result("Chat ID Validation", True, 
                                            f"Chat: {chat_title} (Type: {chat_type})")
                            self.test_results["chat_validation"] = True
                            return True
                    
                    error_data = await response.json()
                    self.print_result("Chat ID Validation", False, 
                                    f"Error: {error_data.get('description', 'Unknown error')}")
                    return False
                    
        except Exception as e:
            self.print_result("Chat ID Validation", False, f"Exception: {str(e)}")
            return False
    
    async def test_basic_message(self) -> bool:
        """Test sending a basic message"""
        try:
            url = f"{self.base_url}/sendMessage"
            
            message = f"""
🧪 <b>Telegram Test Message</b>

✅ <b>Connection Test Successful!</b>

🕐 <b>Timestamp:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 <b>System:</b> XAUUSD AI Trading System
📱 <b>Test Type:</b> Basic Message Test

This message confirms that your Telegram bot is properly configured and can send messages to this chat.
"""
            
            payload = {
                "chat_id": self.chat_id,
                "text": message,
                "parse_mode": "HTML",
                "disable_web_page_preview": True
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('ok'):
                            self.print_result("Basic Message Test", True, "Test message sent successfully")
                            self.test_results["basic_message"] = True
                            return True
                    
                    error_data = await response.json()
                    self.print_result("Basic Message Test", False, 
                                    f"Error: {error_data.get('description', 'Unknown error')}")
                    return False
                    
        except Exception as e:
            self.print_result("Basic Message Test", False, f"Exception: {str(e)}")
            return False
    
    async def test_trading_signal(self) -> bool:
        """Test sending a trading signal notification"""
        try:
            url = f"{self.base_url}/sendMessage"
            
            # Sample trading signal data
            signal_data = {
                "signal": "BUY",
                "confidence": 0.857,
                "current_price": 2456.78,
                "timeframe": "1h",
                "timestamp": datetime.now().isoformat(),
                "model_type": "Enhanced LSTM",
                "market_context": {
                    "trend": "bullish",
                    "volatility": "normal",
                    "rsi_level": "oversold"
                }
            }
            
            # Format trading signal message
            signal_emoji = "🟢" if signal_data["signal"] == "BUY" else "🔴"
            confidence = signal_data["confidence"] * 100
            confidence_emoji = "🔥" if confidence >= 80 else "⚡" if confidence >= 60 else "⚠️"
            
            message = f"""
🤖 <b>XAUUSD AI Trading Signal</b> {signal_emoji}

📊 <b>Signal Details:</b>
• <b>Action:</b> {signal_data["signal"]}
• <b>Confidence:</b> {confidence:.1f}% {confidence_emoji}
• <b>Price:</b> ${signal_data["current_price"]:.2f}
• <b>Timeframe:</b> {signal_data["timeframe"]}
• <b>Model:</b> {signal_data["model_type"]}

📈 <b>Market Context:</b>
• <b>Trend:</b> {signal_data["market_context"]["trend"].title()}
• <b>Volatility:</b> {signal_data["market_context"]["volatility"].title()}
• <b>RSI Level:</b> {signal_data["market_context"]["rsi_level"].title()}

🕐 <b>Generated:</b> {datetime.now().strftime('%H:%M:%S')}

<i>⚠️ This is a test signal for system validation</i>
"""
            
            payload = {
                "chat_id": self.chat_id,
                "text": message,
                "parse_mode": "HTML",
                "disable_web_page_preview": True
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('ok'):
                            self.print_result("Trading Signal Test", True, "Trading signal sent successfully")
                            self.test_results["trading_signal"] = True
                            return True
                    
                    error_data = await response.json()
                    self.print_result("Trading Signal Test", False, 
                                    f"Error: {error_data.get('description', 'Unknown error')}")
                    return False
                    
        except Exception as e:
            self.print_result("Trading Signal Test", False, f"Exception: {str(e)}")
            return False
    
    async def test_system_alert(self) -> bool:
        """Test sending a system alert"""
        try:
            url = f"{self.base_url}/sendMessage"
            
            severity_emoji = {"info": "ℹ️", "warning": "⚠️", "error": "❌", "success": "✅"}
            
            message = f"""
🚨 <b>System Alert</b> {severity_emoji.get("info", "ℹ️")}

<b>Alert Type:</b> System Test
<b>Severity:</b> INFO
<b>Timestamp:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

<b>Message:</b>
This is a test system alert to verify that the XAUUSD AI Trading System can send notifications for system events, errors, and status updates.

<b>System Status:</b> ✅ All systems operational
<b>Next Check:</b> Automated in 5 minutes

<i>This is a test alert for system validation</i>
"""
            
            payload = {
                "chat_id": self.chat_id,
                "text": message,
                "parse_mode": "HTML",
                "disable_web_page_preview": True
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('ok'):
                            self.print_result("System Alert Test", True, "System alert sent successfully")
                            self.test_results["system_alert"] = True
                            return True
                    
                    error_data = await response.json()
                    self.print_result("System Alert Test", False, 
                                    f"Error: {error_data.get('description', 'Unknown error')}")
                    return False
                    
        except Exception as e:
            self.print_result("System Alert Test", False, f"Exception: {str(e)}")
            return False

    def test_api_integration(self) -> bool:
        """Test API integration with Telegram"""
        try:
            # Test if API server is running
            api_endpoints = [
                "http://localhost:8000/health",
                "http://************:8000/health"
            ]

            api_working = False
            for endpoint in api_endpoints:
                try:
                    response = requests.get(endpoint, timeout=5)
                    if response.status_code == 200:
                        api_working = True
                        self.print_result("API Server Check", True, f"API accessible at {endpoint}")
                        break
                except:
                    continue

            if not api_working:
                self.print_result("API Integration Test", False, "API server not accessible")
                return False

            # Test Telegram endpoint if available
            try:
                telegram_test_url = endpoint.replace('/health', '/telegram/test')
                response = requests.post(telegram_test_url, timeout=10)

                if response.status_code == 200:
                    self.print_result("API Integration Test", True, "Telegram API endpoint working")
                    self.test_results["api_integration"] = True
                    return True
                else:
                    self.print_result("API Integration Test", False, f"API returned status {response.status_code}")
                    return False

            except Exception as e:
                self.print_result("API Integration Test", False, f"Telegram API endpoint error: {str(e)}")
                return False

        except Exception as e:
            self.print_result("API Integration Test", False, f"Exception: {str(e)}")
            return False

    def test_webhook_simulation(self) -> bool:
        """Test webhook simulation with Telegram notification"""
        try:
            # Test webhook endpoint
            webhook_endpoints = [
                "http://localhost:8000/api/webhook/tradingview",
                "http://************:8000/api/webhook/tradingview"
            ]

            # Sample TradingView webhook payload
            webhook_payload = {
                "strategy": {
                    "position_size": 1,
                    "order_action": "buy",
                    "order_contracts": 1,
                    "order_price": 2456.78,
                    "order_id": "test_order_123",
                    "market_position": 1,
                    "market_position_size": 1,
                    "prev_market_position": 0,
                    "prev_market_position_size": 0
                },
                "time": datetime.now().isoformat(),
                "ticker": "XAUUSD",
                "timeframe": "1H",
                "close": 2456.78,
                "test_mode": True
            }

            webhook_working = False
            for endpoint in webhook_endpoints:
                try:
                    response = requests.post(endpoint, json=webhook_payload, timeout=10)
                    if response.status_code == 200:
                        webhook_working = True
                        self.print_result("Webhook Test", True, f"Webhook processed at {endpoint}")
                        self.test_results["webhook_test"] = True
                        break
                except:
                    continue

            if not webhook_working:
                self.print_result("Webhook Test", False, "Webhook endpoints not accessible")
                return False

            return True

        except Exception as e:
            self.print_result("Webhook Test", False, f"Exception: {str(e)}")
            return False

    def print_summary(self):
        """Print test summary"""
        self.print_header("TEST SUMMARY")

        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        success_rate = (passed_tests / total_tests) * 100

        print(f"📊 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {total_tests - passed_tests}")
        print(f"📈 Success Rate: {success_rate:.1f}%")

        print(f"\n📋 Detailed Results:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {status} - {test_name.replace('_', ' ').title()}")

        if success_rate >= 80:
            print(f"\n🎉 Telegram integration is working well!")
        elif success_rate >= 60:
            print(f"\n⚠️ Telegram integration has some issues but basic functionality works")
        else:
            print(f"\n❌ Telegram integration needs attention")

        print(f"\n📱 Configuration Used:")
        print(f"  Bot Token: {self.bot_token[:10]}...{self.bot_token[-5:] if self.bot_token else 'Not set'}")
        print(f"  Chat ID: {self.chat_id}")

    async def run_all_tests(self):
        """Run all Telegram tests"""
        self.print_header("XAUUSD AI TRADING SYSTEM - TELEGRAM COMPREHENSIVE TEST")

        print("🔧 Loading configuration...")
        if not self.load_config():
            print("❌ Cannot proceed without Telegram configuration")
            print("\n📋 Setup Instructions:")
            print("1. Run: python setup_telegram.py")
            print("2. Or set environment variables:")
            print("   export TELEGRAM_BOT_TOKEN='your_bot_token'")
            print("   export TELEGRAM_CHAT_ID='your_chat_id'")
            return

        print("🚀 Starting comprehensive tests...\n")

        # Test 1: Bot Token Validation
        self.print_header("Test 1: Bot Token Validation")
        await self.test_bot_token()

        # Test 2: Chat ID Validation
        self.print_header("Test 2: Chat ID Validation")
        await self.test_chat_id()

        # Test 3: Basic Message
        self.print_header("Test 3: Basic Message Test")
        await self.test_basic_message()

        # Wait a bit between messages
        await asyncio.sleep(2)

        # Test 4: Trading Signal
        self.print_header("Test 4: Trading Signal Test")
        await self.test_trading_signal()

        # Wait a bit between messages
        await asyncio.sleep(2)

        # Test 5: System Alert
        self.print_header("Test 5: System Alert Test")
        await self.test_system_alert()

        # Test 6: API Integration
        self.print_header("Test 6: API Integration Test")
        self.test_api_integration()

        # Test 7: Webhook Simulation
        self.print_header("Test 7: Webhook Simulation Test")
        self.test_webhook_simulation()

        # Print summary
        self.print_summary()

async def main():
    """Main test function"""
    tester = TelegramTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)

#!/bin/bash

# XAUUSD AI System - Process Cleanup Script
# This script safely stops all running processes

echo "🧹 XAUUSD AI System - Process Cleanup"
echo "======================================"

# Function to safely kill processes
kill_process_by_port() {
    local port=$1
    local name=$2
    
    echo "🔍 Checking for processes on port $port ($name)..."
    
    # Find processes using the port
    local pids=$(sudo lsof -t -i:$port 2>/dev/null)
    
    if [ -n "$pids" ]; then
        echo "📋 Found processes: $pids"
        
        # Try graceful shutdown first
        echo "🛑 Attempting graceful shutdown..."
        for pid in $pids; do
            sudo kill -TERM $pid 2>/dev/null
        done
        
        # Wait a moment
        sleep 3
        
        # Check if still running
        local remaining=$(sudo lsof -t -i:$port 2>/dev/null)
        if [ -n "$remaining" ]; then
            echo "⚡ Force killing remaining processes..."
            for pid in $remaining; do
                sudo kill -KILL $pid 2>/dev/null
            done
        fi
        
        echo "✅ Cleaned up $name processes on port $port"
    else
        echo "✅ No processes found on port $port"
    fi
}

# Function to kill processes by name
kill_process_by_name() {
    local name=$1
    
    echo "🔍 Checking for $name processes..."
    
    local pids=$(pgrep -f "$name" 2>/dev/null)
    
    if [ -n "$pids" ]; then
        echo "📋 Found $name processes: $pids"
        
        # Try graceful shutdown first
        echo "🛑 Attempting graceful shutdown of $name..."
        pkill -TERM -f "$name" 2>/dev/null
        
        # Wait a moment
        sleep 3
        
        # Check if still running
        local remaining=$(pgrep -f "$name" 2>/dev/null)
        if [ -n "$remaining" ]; then
            echo "⚡ Force killing remaining $name processes..."
            pkill -KILL -f "$name" 2>/dev/null
        fi
        
        echo "✅ Cleaned up $name processes"
    else
        echo "✅ No $name processes found"
    fi
}

# Clean up API server (port 8000)
kill_process_by_port 8000 "API Server"

# Clean up Dashboard (port 3000)
kill_process_by_port 3000 "Dashboard"

# Clean up any Python processes related to our system
kill_process_by_name "api_server.py"
kill_process_by_name "uvicorn"
kill_process_by_name "python.*xauusd"

# Clean up any Node.js processes (dashboard)
kill_process_by_name "node.*dashboard"
kill_process_by_name "npm.*start"

# Clean up any stuck processes
echo "🔍 Checking for any remaining stuck processes..."
sudo pkill -f "xauusd-ai" 2>/dev/null || true

# Verify cleanup
echo ""
echo "🔍 Verification - Checking remaining processes:"
echo "Port 8000 (API):"
sudo lsof -i:8000 2>/dev/null || echo "  ✅ Port 8000 is free"

echo "Port 3000 (Dashboard):"
sudo lsof -i:3000 2>/dev/null || echo "  ✅ Port 3000 is free"

echo ""
echo "✅ Process cleanup completed!"
echo "🚀 You can now restart the services safely."

"""
XAUUSD AI Trading System - Production Telegram Service
Enhanced Telegram notifications for trading signals and system alerts.
"""
import asyncio
import aiohttp
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
from loguru import logger

class TelegramService:
    def __init__(self, bot_token: str, chat_id: str):
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.base_url = f"https://api.telegram.org/bot{bot_token}"
        
    async def send_message(self, message: str, parse_mode: str = "HTML") -> bool:
        """Send message to Telegram"""
        try:
            url = f"{self.base_url}/sendMessage"
            payload = {
                "chat_id": self.chat_id,
                "text": message,
                "parse_mode": parse_mode
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        logger.info("✅ Telegram message sent successfully")
                        return True
                    else:
                        logger.error(f"❌ Telegram API error: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ Failed to send Telegram message: {e}")
            return False
    
    async def send_trading_signal(self, signal_data: Dict[str, Any]) -> bool:
        """Send formatted trading signal to Telegram"""
        try:
            # Extract signal information
            signal = signal_data.get('signal', 'HOLD')
            confidence = signal_data.get('confidence', 0) * 100
            price = signal_data.get('current_price', 0)
            timeframe = signal_data.get('timeframe', '1h')
            timestamp = signal_data.get('timestamp', datetime.now().isoformat())
            
            # Format signal emoji
            signal_emoji = {
                'BUY': '🟢',
                'SELL': '🔴', 
                'HOLD': '🟡'
            }.get(signal, '⚪')
            
            # Confidence color
            confidence_emoji = '🔥' if confidence >= 80 else '⚡' if confidence >= 60 else '💫'
            
            # Create formatted message
            message = f"""
🏆 <b>XAUUSD AI Trading Signal</b>

{signal_emoji} <b>Signal:</b> {signal}
{confidence_emoji} <b>Confidence:</b> {confidence:.1f}%
💰 <b>Price:</b> ${price:.2f}
⏰ <b>Timeframe:</b> {timeframe}
📅 <b>Time:</b> {timestamp.split('T')[0]} {timestamp.split('T')[1][:8]} UTC

📊 <b>Technical Analysis:</b>
• RSI: {signal_data.get('indicators', {}).get('rsi', 'N/A')}
• MACD: {signal_data.get('indicators', {}).get('macd_signal', 'N/A')}
• Bollinger: {signal_data.get('indicators', {}).get('bb_position', 'N/A')}

🎯 <b>Recommendation:</b>
{self._get_recommendation(signal, confidence)}

⚠️ <i>This is an AI-generated signal. Always do your own research and manage risk appropriately.</i>

🔗 <a href="https://greateatfood.com/dashboard/">View Dashboard</a>
            """
            
            return await self.send_message(message.strip())
            
        except Exception as e:
            logger.error(f"❌ Failed to send trading signal: {e}")
            return False
    
    async def send_system_alert(self, alert_type: str, message: str, severity: str = "INFO") -> bool:
        """Send system alert to Telegram"""
        try:
            # Severity emojis
            severity_emoji = {
                'INFO': 'ℹ️',
                'WARNING': '⚠️',
                'ERROR': '❌',
                'SUCCESS': '✅'
            }.get(severity, 'ℹ️')
            
            formatted_message = f"""
{severity_emoji} <b>XAUUSD AI System Alert</b>

<b>Type:</b> {alert_type}
<b>Severity:</b> {severity}
<b>Time:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}

<b>Message:</b>
{message}

🔗 <a href="https://greateatfood.com/dashboard/">Check Dashboard</a>
            """
            
            return await self.send_message(formatted_message.strip())
            
        except Exception as e:
            logger.error(f"❌ Failed to send system alert: {e}")
            return False
    
    async def send_daily_summary(self, summary_data: Dict[str, Any]) -> bool:
        """Send daily trading summary"""
        try:
            signals_today = summary_data.get('signals_today', 0)
            accuracy = summary_data.get('accuracy', 0) * 100
            profit_signals = summary_data.get('profitable_signals', 0)
            
            message = f"""
📈 <b>XAUUSD AI Daily Summary</b>

📊 <b>Today's Performance:</b>
• Signals Generated: {signals_today}
• Profitable Signals: {profit_signals}
• Accuracy Rate: {accuracy:.1f}%

🎯 <b>System Status:</b>
• API: ✅ Healthy
• Model: ✅ Active
• Data Feed: ✅ Live

📅 <b>Date:</b> {datetime.now().strftime('%Y-%m-%d')}

🔗 <a href="https://greateatfood.com/dashboard/">View Full Dashboard</a>
            """
            
            return await self.send_message(message.strip())
            
        except Exception as e:
            logger.error(f"❌ Failed to send daily summary: {e}")
            return False
    
    def _get_recommendation(self, signal: str, confidence: float) -> str:
        """Get trading recommendation based on signal and confidence"""
        if signal == 'BUY':
            if confidence >= 80:
                return "🔥 Strong BUY signal - Consider entering long position"
            elif confidence >= 60:
                return "⚡ Moderate BUY signal - Wait for confirmation"
            else:
                return "💫 Weak BUY signal - Exercise caution"
        elif signal == 'SELL':
            if confidence >= 80:
                return "🔥 Strong SELL signal - Consider entering short position"
            elif confidence >= 60:
                return "⚡ Moderate SELL signal - Wait for confirmation"
            else:
                return "💫 Weak SELL signal - Exercise caution"
        else:
            return "🟡 HOLD - Wait for clearer market direction"
    
    async def test_connection(self) -> bool:
        """Test Telegram bot connection"""
        try:
            test_message = f"""
🧪 <b>XAUUSD AI System Test</b>

✅ Telegram connection successful!
🕐 Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}
🤖 Bot is ready to send trading signals

🔗 <a href="https://greateatfood.com/dashboard/">Dashboard</a>
            """
            
            result = await self.send_message(test_message.strip())
            if result:
                logger.info("✅ Telegram connection test successful")
            return result
            
        except Exception as e:
            logger.error(f"❌ Telegram connection test failed: {e}")
            return False

# Global telegram service instance
telegram_service = None

def initialize_telegram(bot_token: str, chat_id: str) -> TelegramService:
    """Initialize global telegram service"""
    global telegram_service
    telegram_service = TelegramService(bot_token, chat_id)
    return telegram_service

async def send_signal_notification(signal_data: Dict[str, Any]) -> bool:
    """Send trading signal notification"""
    if telegram_service:
        return await telegram_service.send_trading_signal(signal_data)
    return False

async def send_alert_notification(alert_type: str, message: str, severity: str = "INFO") -> bool:
    """Send system alert notification"""
    if telegram_service:
        return await telegram_service.send_system_alert(alert_type, message, severity)
    return False

async def test_telegram_connection() -> bool:
    """Test telegram connection"""
    if telegram_service:
        return await telegram_service.test_connection()
    return False

if __name__ == "__main__":
    # Test the service
    import sys
    
    if len(sys.argv) != 3:
        print("Usage: python telegram_service_production.py <BOT_TOKEN> <CHAT_ID>")
        sys.exit(1)
    
    bot_token = sys.argv[1]
    chat_id = sys.argv[2]
    
    # Initialize and test
    service = initialize_telegram(bot_token, chat_id)
    
    # Run test
    async def test():
        result = await test_telegram_connection()
        if result:
            print("✅ Telegram service is working!")
        else:
            print("❌ Telegram service failed!")
    
    asyncio.run(test())

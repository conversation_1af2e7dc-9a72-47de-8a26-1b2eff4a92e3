#!/bin/bash

# ============================================================================
# XAUUSD AI Trading System - Telegram Production Deployment
# Deploy enhanced API with Telegram integration to production server
# ============================================================================

echo "🚀 Deploying XAUUSD AI Trading System with Telegram Integration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Get Telegram credentials
echo -e "${BLUE}📱 Telegram Bot Configuration${NC}"
echo "Please provide your Telegram bot credentials:"
read -p "Bot Token (from @BotFather): " BOT_TOKEN
read -p "Chat ID (your Telegram user ID): " CHAT_ID

if [[ -z "$BOT_TOKEN" || -z "$CHAT_ID" ]]; then
    print_error "Bot token and chat ID are required!"
    exit 1
fi

# Validate bot token format
if [[ ! $BOT_TOKEN =~ ^[0-9]+:[A-Za-z0-9_-]+$ ]]; then
    print_error "Invalid bot token format!"
    exit 1
fi

print_status "Telegram credentials provided ✅"

# Navigate to system directory
cd /home/<USER>/xauusd-ai-system || {
    print_error "System directory not found!"
    exit 1
}

print_status "Navigating to system directory..."

# Backup existing files
print_status "Creating backup of existing files..."
cp api.py api.py.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

# Copy new files from current directory
print_status "Deploying new files..."

# Get the directory where the script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Copy Telegram service
if [[ -f "$SCRIPT_DIR/telegram_service_production.py" ]]; then
    cp "$SCRIPT_DIR/telegram_service_production.py" . || {
        print_error "Failed to copy telegram service file!"
        exit 1
    }
else
    print_error "telegram_service_production.py not found in $SCRIPT_DIR"
    exit 1
fi

# Copy enhanced API
if [[ -f "$SCRIPT_DIR/enhanced_api_with_telegram.py" ]]; then
    cp "$SCRIPT_DIR/enhanced_api_with_telegram.py" api.py || {
        print_error "Failed to copy enhanced API file!"
        exit 1
    }
else
    print_error "enhanced_api_with_telegram.py not found in $SCRIPT_DIR"
    exit 1
fi

print_status "Files deployed successfully ✅"

# Install additional Python dependencies
print_status "Installing additional dependencies..."
source venv/bin/activate

pip install aiohttp loguru python-multipart || {
    print_error "Failed to install dependencies!"
    exit 1
}

print_status "Dependencies installed ✅"

# Create environment file for Telegram credentials
print_status "Creating environment configuration..."
cat > .env << EOF
# Telegram Configuration
TELEGRAM_BOT_TOKEN=$BOT_TOKEN
TELEGRAM_CHAT_ID=$CHAT_ID

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=false

# System Configuration
ENVIRONMENT=production
LOG_LEVEL=info
EOF

print_status "Environment configuration created ✅"

# Update supervisor configuration
print_status "Updating supervisor configuration..."
sudo tee /etc/supervisor/conf.d/xauusd-ai-api.conf > /dev/null << EOF
[program:xauusd-ai-api]
command=/home/<USER>/xauusd-ai-system/venv/bin/python api.py
directory=/home/<USER>/xauusd-ai-system
user=xauusd-ai
autostart=true
autorestart=true
stderr_logfile=/var/log/xauusd-ai-api.err.log
stdout_logfile=/var/log/xauusd-ai-api.out.log
environment=TELEGRAM_BOT_TOKEN="$BOT_TOKEN",TELEGRAM_CHAT_ID="$CHAT_ID"
EOF

print_status "Supervisor configuration updated ✅"

# Test the Telegram service
print_status "Testing Telegram connection..."
python3 -c "
import asyncio
import sys
sys.path.append('.')
from telegram_service_production import initialize_telegram, test_telegram_connection

async def test():
    service = initialize_telegram('$BOT_TOKEN', '$CHAT_ID')
    result = await test_telegram_connection()
    if result:
        print('✅ Telegram connection successful!')
        return True
    else:
        print('❌ Telegram connection failed!')
        return False

result = asyncio.run(test())
sys.exit(0 if result else 1)
" || {
    print_warning "Telegram connection test failed, but continuing deployment..."
}

# Restart services
print_status "Restarting services..."

# Reload supervisor
sudo supervisorctl reread
sudo supervisorctl update

# Stop existing services
sudo supervisorctl stop xauusd-ai-api 2>/dev/null || true

# Start API service
sudo supervisorctl start xauusd-ai-api || {
    print_error "Failed to start API service!"
    print_error "Check logs: sudo supervisorctl tail -f xauusd-ai-api"
    exit 1
}

# Wait for service to start
sleep 5

# Check service status
if sudo supervisorctl status xauusd-ai-api | grep -q "RUNNING"; then
    print_status "API service started successfully ✅"
else
    print_error "API service failed to start!"
    sudo supervisorctl tail xauusd-ai-api
    exit 1
fi

# Test API endpoints
print_status "Testing API endpoints..."

# Test health endpoint
if curl -s http://localhost:8000/health | grep -q "healthy"; then
    print_status "Health endpoint working ✅"
else
    print_warning "Health endpoint test failed"
fi

# Test Telegram endpoint
if curl -s http://localhost:8000/telegram/test | grep -q "success"; then
    print_status "Telegram endpoint working ✅"
else
    print_warning "Telegram endpoint test failed"
fi

# Update nginx configuration for new endpoints
print_status "Updating nginx configuration..."
sudo tee -a /etc/nginx/sites-available/default > /dev/null << 'EOF'

    # Telegram webhook endpoint
    location /api/webhook/tradingview {
        proxy_pass http://localhost:8000/webhook/tradingview;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # API endpoints
    location /api/ {
        proxy_pass http://localhost:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
EOF

# Test nginx configuration
sudo nginx -t && sudo systemctl reload nginx || {
    print_warning "Nginx configuration update failed"
}

print_status "Nginx configuration updated ✅"

# Final tests
print_status "Running final tests..."

# Test external webhook endpoint
if curl -s https://greateatfood.com/api/health | grep -q "healthy"; then
    print_status "External API access working ✅"
else
    print_warning "External API access test failed"
fi

# Display deployment summary
echo ""
echo -e "${GREEN}🎉 DEPLOYMENT COMPLETED SUCCESSFULLY! 🎉${NC}"
echo ""
echo -e "${BLUE}📊 System Status:${NC}"
echo "✅ Telegram Service: Deployed"
echo "✅ Enhanced API: Running"
echo "✅ Webhook Endpoint: Active"
echo "✅ Dashboard: Operational"
echo ""
echo -e "${BLUE}🔗 Important URLs:${NC}"
echo "• Dashboard: https://greateatfood.com/dashboard/"
echo "• API Health: https://greateatfood.com/api/health"
echo "• Webhook URL: https://greateatfood.com/api/webhook/tradingview"
echo "• API Docs: https://greateatfood.com/api/docs"
echo ""
echo -e "${BLUE}📱 Telegram Integration:${NC}"
echo "• Bot Token: ${BOT_TOKEN:0:10}..."
echo "• Chat ID: $CHAT_ID"
echo "• Test Endpoint: https://greateatfood.com/api/telegram/test"
echo ""
echo -e "${BLUE}📈 TradingView Setup:${NC}"
echo "1. Copy the Pine Script from: xauusd_ai_indicator.pine"
echo "2. Create new indicator in TradingView"
echo "3. Set webhook URL: https://greateatfood.com/api/webhook/tradingview"
echo "4. Enable alerts for BUY/SELL signals"
echo ""
echo -e "${YELLOW}⚠️  Next Steps:${NC}"
echo "1. Test Telegram notifications"
echo "2. Set up TradingView indicator"
echo "3. Configure alert webhooks"
echo "4. Monitor system logs"
echo ""
echo -e "${GREEN}🚀 Your XAUUSD AI Trading System with Telegram is now LIVE!${NC}"

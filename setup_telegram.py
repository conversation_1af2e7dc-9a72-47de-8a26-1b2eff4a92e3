"""
Telegram Bot Setup Script for XAUUSD AI Trading System
Helps users create and configure Telegram bot for notifications
"""

import os
import asyncio
import aiohttp
from loguru import logger
import sys

# Setup logging
logger.remove()
logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>")

class TelegramSetup:
    """Helper class for setting up Telegram bot"""
    
    def __init__(self):
        self.bot_token = None
        self.chat_id = None
        
    async def test_bot_token(self, token: str) -> tuple[bool, dict]:
        """Test if bot token is valid"""
        try:
            url = f"https://api.telegram.org/bot{token}/getMe"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        return True, data.get('result', {})
                    else:
                        return False, {}
                        
        except Exception as e:
            logger.error(f"Error testing bot token: {e}")
            return False, {}
    
    async def get_chat_id(self, token: str) -> str:
        """Get chat ID by asking user to send a message"""
        try:
            url = f"https://api.telegram.org/bot{token}/getUpdates"
            
            print("\n📱 To get your Chat ID:")
            print("1. Open Telegram and find your bot")
            print("2. Send any message to your bot (e.g., 'Hello')")
            print("3. Press Enter here to continue...")
            input()
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        updates = data.get('result', [])
                        
                        if updates:
                            # Get the most recent message
                            latest_update = updates[-1]
                            chat = latest_update.get('message', {}).get('chat', {})
                            chat_id = str(chat.get('id', ''))
                            chat_type = chat.get('type', '')
                            
                            if chat_id:
                                logger.info(f"✅ Found Chat ID: {chat_id} (Type: {chat_type})")
                                return chat_id
                            else:
                                logger.error("❌ No chat ID found in recent messages")
                                return ""
                        else:
                            logger.error("❌ No messages found. Please send a message to your bot first.")
                            return ""
                    else:
                        logger.error(f"❌ Failed to get updates: {response.status}")
                        return ""
                        
        except Exception as e:
            logger.error(f"Error getting chat ID: {e}")
            return ""
    
    async def send_test_message(self, token: str, chat_id: str) -> bool:
        """Send a test message to verify setup"""
        try:
            url = f"https://api.telegram.org/bot{token}/sendMessage"
            
            message = """
🤖 <b>XAUUSD AI Trading System</b>

✅ <b>Telegram Setup Complete!</b>

Your bot is now configured and ready to send trading signals and system alerts.

🎯 <b>What you'll receive:</b>
• Real-time trading signals (BUY/SELL)
• Signal confidence levels
• Market context analysis
• System status alerts
• Daily trading summaries

📊 <b>Next Steps:</b>
1. Start your AI trading system
2. Configure your trading parameters
3. Begin receiving live signals!

💡 <i>This is a test message to confirm your setup is working correctly.</i>
            """.strip()
            
            payload = {
                "chat_id": chat_id,
                "text": message,
                "parse_mode": "HTML",
                "disable_web_page_preview": True
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        logger.info("✅ Test message sent successfully!")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ Failed to send test message: {response.status} - {error_text}")
                        return False
                        
        except Exception as e:
            logger.error(f"Error sending test message: {e}")
            return False
    
    def save_to_env_file(self, token: str, chat_id: str):
        """Save configuration to .env file"""
        try:
            env_content = []
            env_file = ".env"
            
            # Read existing .env file if it exists
            if os.path.exists(env_file):
                with open(env_file, 'r') as f:
                    env_content = f.readlines()
            
            # Remove existing Telegram configuration
            env_content = [line for line in env_content if not line.startswith('TELEGRAM_')]
            
            # Add new Telegram configuration
            env_content.append(f"\n# Telegram Configuration\n")
            env_content.append(f"TELEGRAM_BOT_TOKEN={token}\n")
            env_content.append(f"TELEGRAM_CHAT_ID={chat_id}\n")
            
            # Write back to .env file
            with open(env_file, 'w') as f:
                f.writelines(env_content)
            
            logger.info(f"✅ Configuration saved to {env_file}")
            
        except Exception as e:
            logger.error(f"Error saving to .env file: {e}")
    
    def print_manual_setup(self, token: str, chat_id: str):
        """Print manual setup instructions"""
        print("\n" + "="*60)
        print("📋 MANUAL SETUP INSTRUCTIONS")
        print("="*60)
        print("\nAdd these environment variables to your system:")
        print(f"\nTELEGRAM_BOT_TOKEN={token}")
        print(f"TELEGRAM_CHAT_ID={chat_id}")
        print("\nOr add them to your .env file:")
        print(f"\necho 'TELEGRAM_BOT_TOKEN={token}' >> .env")
        print(f"echo 'TELEGRAM_CHAT_ID={chat_id}' >> .env")
        print("\n" + "="*60)

async def main():
    """Main setup function"""
    print("🤖 XAUUSD AI Trading System - Telegram Setup")
    print("="*50)
    
    setup = TelegramSetup()
    
    # Step 1: Get bot token
    print("\n📋 Step 1: Bot Token")
    print("To create a Telegram bot:")
    print("1. Open Telegram and search for @BotFather")
    print("2. Send /newbot command")
    print("3. Follow instructions to create your bot")
    print("4. Copy the bot token provided by BotFather")
    
    while True:
        token = input("\n🔑 Enter your bot token: ").strip()
        if not token:
            print("❌ Bot token cannot be empty")
            continue
        
        print("🔍 Testing bot token...")
        is_valid, bot_info = await setup.test_bot_token(token)
        
        if is_valid:
            bot_name = bot_info.get('first_name', 'Unknown')
            bot_username = bot_info.get('username', 'Unknown')
            logger.info(f"✅ Bot token valid! Bot: {bot_name} (@{bot_username})")
            setup.bot_token = token
            break
        else:
            print("❌ Invalid bot token. Please check and try again.")
    
    # Step 2: Get chat ID
    print("\n📱 Step 2: Chat ID")
    chat_id = await setup.get_chat_id(token)
    
    if not chat_id:
        print("\n❌ Could not automatically get Chat ID.")
        print("Manual method:")
        print("1. Send a message to your bot")
        print("2. Visit: https://api.telegram.org/bot{}/getUpdates".format(token))
        print("3. Look for 'chat':{'id': YOUR_CHAT_ID}")
        
        while True:
            manual_chat_id = input("\n📱 Enter your Chat ID manually: ").strip()
            if manual_chat_id:
                chat_id = manual_chat_id
                break
            print("❌ Chat ID cannot be empty")
    
    setup.chat_id = chat_id
    
    # Step 3: Test setup
    print("\n🧪 Step 3: Testing Setup")
    print("Sending test message...")
    
    success = await setup.send_test_message(token, chat_id)
    
    if success:
        print("✅ Test message sent! Check your Telegram.")
        
        # Step 4: Save configuration
        print("\n💾 Step 4: Save Configuration")
        save_choice = input("Save to .env file? (y/n): ").strip().lower()
        
        if save_choice in ['y', 'yes']:
            setup.save_to_env_file(token, chat_id)
        else:
            setup.print_manual_setup(token, chat_id)
        
        print("\n🎉 Telegram Setup Complete!")
        print("\n📋 Next Steps:")
        print("1. Restart your AI trading system")
        print("2. Test the connection using: python telegram_service.py")
        print("3. Or test via API: POST /telegram/test")
        print("4. Start receiving live trading signals!")
        
    else:
        print("❌ Test message failed. Please check your configuration.")
        setup.print_manual_setup(token, chat_id)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 Setup cancelled by user")
    except Exception as e:
        logger.error(f"Setup error: {e}")
        print(f"\n❌ Setup failed: {e}")

# 🚨 XAUUSD AI Production Quick Fix Guide

## 🔍 Issues Identified from Screenshots

1. **Dashboard UI not loading** - Missing dashboard.py file and nginx config
2. **No trained model available** - Trainer service exited
3. **Services running but not accessible** - Nginx routing issues

## ⚡ Quick Fix Commands (Run on your EC2 server)

### Step 1: Install Missing Dependencies
```bash
# Activate virtual environment
cd /home/<USER>/xauusd-ai-system
source venv/bin/activate

# Install missing packages
pip install flask psutil pydantic-settings loguru
```

### Step 2: Create Dashboard File
```bash
# The dashboard.py file has been created in your local TRVBOT directory
# Copy it to your EC2 server:
# 1. Copy the dashboard.py content from your local TRVBOT folder
# 2. Create it on your EC2 server:

cat > /home/<USER>/xauusd-ai-system/dashboard.py << 'EOF'
# [Copy the entire content of dashboard.py from your local system]
EOF
```

### Step 3: Fix Nginx Configuration
```bash
# Update nginx configuration
sudo tee /etc/nginx/sites-available/xauusd-ai << 'EOF'
server {
    listen 80;
    server_name greateatfood.com www.greateatfood.com;

    # API endpoints
    location /api/ {
        proxy_pass http://127.0.0.1:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Dashboard
    location /dashboard/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Health check
    location /health {
        proxy_pass http://127.0.0.1:8000/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Root redirects to dashboard
    location / {
        return 301 /dashboard/;
    }
}
EOF

# Test and reload nginx
sudo nginx -t
sudo systemctl reload nginx
```

### Step 4: Create Quick Model for Testing
```bash
# Create a simple model file for immediate testing
cd /home/<USER>/xauusd-ai-system
mkdir -p models

# Create dummy model
cat > create_dummy_model.py << 'EOF'
import pickle
import numpy as np
from pathlib import Path

class DummyModel:
    def __init__(self):
        self.model_type = "dummy_lstm"
        self.accuracy = 0.75
    
    def predict(self, X):
        # Return random predictions for testing
        return np.random.random((len(X), 1))

# Create and save dummy model
model = DummyModel()
with open('models/xauusd_model.h5', 'wb') as f:
    pickle.dump(model, f)

print("✅ Dummy model created successfully")
EOF

# Run the dummy model creation
python create_dummy_model.py
```

### Step 5: Restart All Services
```bash
# Restart all services
sudo supervisorctl restart xauusd-ai-api
sudo supervisorctl restart xauusd-ai-dashboard
sudo supervisorctl restart xauusd-ai-data-collector

# Check status
sudo supervisorctl status
```

### Step 6: Test Everything
```bash
# Test API directly
curl http://localhost:8000/health

# Test dashboard directly
curl http://localhost:3000

# Test through nginx
curl http://greateatfood.com/api/health
curl http://greateatfood.com/dashboard/
```

## 🌐 Expected Access Points After Fix

1. **Main Dashboard**: http://greateatfood.com/dashboard/
2. **API Health**: http://greateatfood.com/api/health
3. **Direct API**: http://greateatfood.com:8000/health
4. **Direct Dashboard**: http://greateatfood.com:3000/

## 🔧 Alternative Manual Steps

If the above doesn't work, try these manual steps:

### Check Service Logs
```bash
# Check individual service logs
sudo supervisorctl tail -f xauusd-ai-api
sudo supervisorctl tail -f xauusd-ai-dashboard
sudo supervisorctl tail -f xauusd-ai-data-collector

# Check nginx logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log
```

### Manual Service Start
```bash
# Stop supervisor services
sudo supervisorctl stop all

# Start API manually for testing
cd /home/<USER>/xauusd-ai-system
source venv/bin/activate
python api_server.py &

# Start dashboard manually
python dashboard.py &

# Check if they're running
ps aux | grep python
```

### Port Check
```bash
# Check what's running on ports
sudo netstat -tlnp | grep :8000
sudo netstat -tlnp | grep :3000
sudo netstat -tlnp | grep :80
```

## 🎯 Quick Verification Commands

After running the fixes, verify everything works:

```bash
# 1. Check services are running
sudo supervisorctl status

# 2. Check API responds
curl -s http://localhost:8000/health | jq .

# 3. Check dashboard responds
curl -s http://localhost:3000 | head -20

# 4. Check nginx routing
curl -s http://greateatfood.com/api/health | jq .

# 5. Check dashboard through nginx
curl -s http://greateatfood.com/dashboard/ | head -20
```

## 🚀 Expected Results

After successful fix:
- ✅ API returns: `{"status": "healthy", "timestamp": "..."}`
- ✅ Dashboard shows: HTML page with system metrics
- ✅ All supervisor services show: RUNNING
- ✅ Nginx routes correctly to both API and dashboard

## 📞 If Still Having Issues

1. **Check the logs**: `tail -f logs/production.log`
2. **Verify file permissions**: `ls -la dashboard.py models/`
3. **Check Python path**: `which python` in venv
4. **Verify nginx syntax**: `sudo nginx -t`

The main issue is that the dashboard.py file was missing and nginx wasn't configured to route to the dashboard properly. These fixes should resolve both issues.

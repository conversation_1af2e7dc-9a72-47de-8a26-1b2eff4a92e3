#!/usr/bin/env python3
"""
Quick API Connection Test Script
Tests both local and external API connectivity
"""

import requests
import json
import time
from datetime import datetime

def test_endpoint(url, description):
    """Test a single endpoint"""
    print(f"\n🧪 Testing {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print(f"✅ SUCCESS - Status: {response.status_code}")
            
            # Try to parse JSON
            try:
                data = response.json()
                print(f"📄 Response: {json.dumps(data, indent=2)}")
            except:
                print(f"📄 Response (text): {response.text[:200]}...")
                
        else:
            print(f"❌ FAILED - Status: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}...")
            
    except requests.exceptions.ConnectTimeout:
        print("❌ FAILED - Connection timeout")
    except requests.exceptions.ConnectionError:
        print("❌ FAILED - Connection error")
    except Exception as e:
        print(f"❌ FAILED - Error: {e}")

def test_webhook_endpoint(url):
    """Test webhook endpoint with POST request"""
    print(f"\n🧪 Testing Webhook POST")
    print(f"URL: {url}")
    
    # Sample TradingView webhook payload
    test_payload = {
        "signal": "BUY",
        "price": 2650.50,
        "timeframe": "1h",
        "timestamp": datetime.now().isoformat(),
        "confidence": 0.85,
        "indicator_data": {
            "rsi": 65,
            "macd_line": 0.5,
            "macd_signal": 0.3,
            "bb_position": "middle",
            "trend": "bullish",
            "volume_ratio": 1.2
        }
    }
    
    try:
        response = requests.post(
            url, 
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code in [200, 201]:
            print(f"✅ SUCCESS - Status: {response.status_code}")
            try:
                data = response.json()
                print(f"📄 Response: {json.dumps(data, indent=2)}")
            except:
                print(f"📄 Response (text): {response.text[:200]}...")
        else:
            print(f"❌ FAILED - Status: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ FAILED - Error: {e}")

def main():
    """Main test function"""
    print("🚀 XAUUSD AI Trading System - API Connection Test")
    print("=" * 60)
    
    # Test endpoints
    endpoints = [
        ("http://localhost:8000/health", "Local API Health"),
        ("http://localhost:8000/", "Local API Root"),
        ("http://localhost:3000", "Local Dashboard"),
        ("http://greateatfood.com/health", "External API Health"),
        ("http://greateatfood.com/api/health", "External API Health (via Nginx)"),
        ("http://greateatfood.com/dashboard/", "External Dashboard"),
        ("http://************/health", "Direct IP Health"),
        ("http://************:8000/health", "Direct IP:Port Health"),
    ]
    
    for url, description in endpoints:
        test_endpoint(url, description)
        time.sleep(1)  # Small delay between tests
    
    # Test webhook endpoints
    webhook_endpoints = [
        "http://localhost:8000/webhook/tradingview",
        "http://greateatfood.com/api/webhook/tradingview",
        "http://************:8000/webhook/tradingview"
    ]
    
    for url in webhook_endpoints:
        test_webhook_endpoint(url)
        time.sleep(1)
    
    print("\n" + "=" * 60)
    print("🏁 Test completed!")
    print("\n📋 Next steps if tests fail:")
    print("   1. Run: chmod +x fix_server_access.sh && ./fix_server_access.sh")
    print("   2. Check logs: tail -f logs/api_server.log")
    print("   3. Check nginx: sudo nginx -t && sudo systemctl status nginx")
    print("   4. Check firewall: sudo ufw status")

if __name__ == "__main__":
    main()

"""
Setup script for XAUUSD AI Trading System
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path
from loguru import logger


def setup_logging():
    """Setup logging for setup script"""
    logger.remove()
    logger.add(
        sys.stdout,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 11):
        logger.error("Python 3.11 or higher is required")
        return False
    
    logger.info(f"✓ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True


def create_directories():
    """Create necessary directories"""
    directories = [
        "models",
        "logs", 
        "data",
        "data/raw",
        "data/processed",
        "data/backups"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"✓ Created directory: {directory}")


def install_dependencies(dev_mode=False):
    """Install Python dependencies"""
    try:
        logger.info("Installing Python dependencies...")
        
        # Upgrade pip first
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # Install requirements
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        
        # Install development dependencies if requested
        if dev_mode and os.path.exists("requirements-dev.txt"):
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements-dev.txt"], 
                          check=True, capture_output=True)
        
        logger.info("✓ Dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install dependencies: {e}")
        return False


def setup_environment():
    """Setup environment configuration"""
    try:
        # Copy .env.example to .env if it doesn't exist
        if not os.path.exists(".env"):
            if os.path.exists(".env.example"):
                import shutil
                shutil.copy(".env.example", ".env")
                logger.info("✓ Created .env file from .env.example")
                logger.warning("⚠️  Please edit .env file with your configuration")
            else:
                logger.warning("⚠️  .env.example not found, please create .env manually")
        else:
            logger.info("✓ .env file already exists")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to setup environment: {e}")
        return False


def test_data_collection():
    """Test data collection functionality"""
    try:
        logger.info("Testing data collection...")
        
        from data_collector import XAUUSDDataCollector
        
        collector = XAUUSDDataCollector()
        data = collector.fetch_yfinance_data(timeframe="1h", period="5d")
        
        if not data.empty:
            logger.info(f"✓ Data collection test passed ({len(data)} records)")
            return True
        else:
            logger.error("✗ Data collection test failed - no data retrieved")
            return False
            
    except Exception as e:
        logger.error(f"✗ Data collection test failed: {e}")
        return False


def test_model_creation():
    """Test AI model creation"""
    try:
        logger.info("Testing AI model creation...")
        
        from ai_model import XAUUSDPredictor
        
        # Test LSTM model creation
        predictor = XAUUSDPredictor(model_type="lstm")
        model = predictor.build_lstm_model((60, 20))  # Sample input shape
        
        if model is not None:
            logger.info("✓ LSTM model creation test passed")
            return True
        else:
            logger.error("✗ LSTM model creation test failed")
            return False
            
    except Exception as e:
        logger.error(f"✗ Model creation test failed: {e}")
        return False


def test_api_server():
    """Test API server startup"""
    try:
        logger.info("Testing API server...")
        
        # Import to check for syntax errors
        import api_server
        logger.info("✓ API server import test passed")
        
        # Note: We don't actually start the server in setup
        return True
        
    except Exception as e:
        logger.error(f"✗ API server test failed: {e}")
        return False


def create_sample_config():
    """Create sample configuration files"""
    try:
        # Create sample training script
        sample_train_script = """#!/bin/bash
# Sample training script
echo "Starting XAUUSD AI model training..."
python train_model.py --timeframe all --model-type lstm
echo "Training completed!"
"""
        
        with open("train_models.sh", "w") as f:
            f.write(sample_train_script)
        
        # Make it executable on Unix systems
        if os.name != 'nt':
            os.chmod("train_models.sh", 0o755)
        
        logger.info("✓ Created sample training script")
        
        # Create sample systemd service file
        service_content = """[Unit]
Description=XAUUSD AI Trading Signal Service
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/TRVBOT
Environment=PATH=/path/to/venv/bin
ExecStart=/path/to/venv/bin/python api_server.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
        
        with open("xauusd-ai.service", "w") as f:
            f.write(service_content)
        
        logger.info("✓ Created sample systemd service file")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create sample config: {e}")
        return False


def run_initial_training(quick_mode=False):
    """Run initial model training"""
    try:
        if quick_mode:
            logger.info("Skipping initial training in quick mode")
            return True
        
        logger.info("Running initial model training...")
        logger.info("This may take several minutes...")
        
        # Run training for 1h timeframe only for initial setup
        result = subprocess.run([
            sys.executable, "train_model.py", 
            "--timeframe", "1h", 
            "--model-type", "lstm"
        ], capture_output=True, text=True, timeout=600)  # 10 minute timeout
        
        if result.returncode == 0:
            logger.info("✓ Initial training completed successfully")
            return True
        else:
            logger.error(f"✗ Initial training failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("✗ Initial training timed out")
        return False
    except Exception as e:
        logger.error(f"✗ Initial training failed: {e}")
        return False


def print_next_steps():
    """Print next steps for user"""
    logger.info("\n" + "="*60)
    logger.info("🎉 XAUUSD AI Trading System Setup Complete!")
    logger.info("="*60)
    
    logger.info("\n📋 Next Steps:")
    logger.info("1. Edit .env file with your configuration:")
    logger.info("   - Set TRADINGVIEW_WEBHOOK_URL")
    logger.info("   - Set WEBHOOK_SECRET")
    logger.info("   - Configure other parameters as needed")
    
    logger.info("\n2. Start the API server:")
    logger.info("   python api_server.py")
    
    logger.info("\n3. Train models for all timeframes:")
    logger.info("   python train_model.py --timeframe all")
    
    logger.info("\n4. Add Pine Script indicator to TradingView:")
    logger.info("   - Copy content from xauusd_ai_indicator.pine")
    logger.info("   - Add to TradingView Pine Editor")
    
    logger.info("\n5. Set up webhooks in TradingView:")
    logger.info("   - Configure alerts to send to your API")
    logger.info("   - Test webhook integration")
    
    logger.info("\n6. Monitor the system:")
    logger.info("   - Check logs in logs/ directory")
    logger.info("   - Monitor API at http://localhost:8000")
    logger.info("   - Use /health endpoint for health checks")
    
    logger.info("\n📚 Documentation:")
    logger.info("   - README.md - Complete setup guide")
    logger.info("   - API docs at http://localhost:8000/docs")
    
    logger.info("\n🐳 Docker Deployment:")
    logger.info("   docker-compose up -d")
    
    logger.info("\n" + "="*60)


def main():
    """Main setup function"""
    parser = argparse.ArgumentParser(description="Setup XAUUSD AI Trading System")
    parser.add_argument("--dev", action="store_true", help="Install development dependencies")
    parser.add_argument("--quick", action="store_true", help="Quick setup without training")
    parser.add_argument("--no-test", action="store_true", help="Skip functionality tests")
    
    args = parser.parse_args()
    
    setup_logging()
    
    logger.info("🚀 Starting XAUUSD AI Trading System Setup")
    logger.info("="*50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install dependencies
    if not install_dependencies(args.dev):
        logger.error("Setup failed during dependency installation")
        sys.exit(1)
    
    # Setup environment
    if not setup_environment():
        logger.error("Setup failed during environment configuration")
        sys.exit(1)
    
    # Run tests unless skipped
    if not args.no_test:
        logger.info("\n🧪 Running functionality tests...")
        
        tests = [
            ("Data Collection", test_data_collection),
            ("Model Creation", test_model_creation),
            ("API Server", test_api_server)
        ]
        
        for test_name, test_func in tests:
            if not test_func():
                logger.error(f"Setup failed during {test_name} test")
                logger.warning("You may need to check your configuration")
    
    # Create sample configuration
    create_sample_config()
    
    # Run initial training unless in quick mode
    if not args.quick:
        logger.info("\n🧠 Running initial model training...")
        run_initial_training(quick_mode=args.quick)
    
    # Print next steps
    print_next_steps()
    
    logger.info("✅ Setup completed successfully!")


if __name__ == "__main__":
    main()

"""
Real-time Signal Generation Service for XAUUSD Trading
"""
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import pandas as pd
import requests
from loguru import logger
import schedule
from threading import Thread

from data_collector import XAUUSDDataCollector
from ai_model import XAUUSDPredictor
from config import settings


class SignalGenerator:
    """Generates real-time trading signals for XAUUSD"""
    
    def __init__(self):
        self.data_collector = XAUUSDDataCollector()
        self.predictors = {}
        self.last_signals = {}
        self.signal_history = []
        self.is_running = False
        
        # Initialize predictors for each timeframe
        for timeframe in settings.TIMEFRAMES:
            self.predictors[timeframe] = XAUUSDPredictor(model_type=settings.MODEL_TYPE)
            self.last_signals[timeframe] = None
    
    def initialize_models(self):
        """Initialize and load AI models for all timeframes"""
        try:
            logger.info("Initializing AI models...")
            
            for timeframe in settings.TIMEFRAMES:
                logger.info(f"Loading model for {timeframe} timeframe")
                self.predictors[timeframe].load_model()
                
            logger.info("All models initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing models: {e}")
            return False
    
    def generate_signal(self, timeframe: str) -> Optional[Dict]:
        """Generate trading signal for specific timeframe"""
        try:
            logger.info(f"Generating signal for {timeframe} timeframe")
            
            # Get latest data
            data = self.data_collector.get_processed_data(timeframe=timeframe)
            
            if data.empty:
                logger.warning(f"No data available for {timeframe}")
                return None
            
            # Get timeframe-specific parameters
            tf_params = settings.TIMEFRAME_PARAMS.get(timeframe, {})
            
            # Generate prediction
            predictor = self.predictors[timeframe]
            prediction = predictor.predict(data)
            
            if not prediction:
                logger.warning(f"No prediction generated for {timeframe}")
                return None
            
            # Apply timeframe-specific thresholds
            signal_threshold = tf_params.get('signal_threshold', settings.SIGNAL_THRESHOLD)
            confidence_threshold = tf_params.get('confidence_threshold', settings.CONFIDENCE_THRESHOLD)
            
            # Filter signals based on confidence
            if prediction['confidence'] < confidence_threshold:
                logger.info(f"Signal filtered out due to low confidence: {prediction['confidence']:.3f}")
                return None
            
            # Add timeframe and market context
            current_price = data['close'].iloc[-1]
            signal = {
                'timeframe': timeframe,
                'signal': prediction['signal'],
                'confidence': prediction['confidence'],
                'probability': prediction['probability'],
                'current_price': float(current_price),
                'timestamp': prediction['timestamp'],
                'model_type': prediction['model_type'],
                'market_context': self._get_market_context(data)
            }
            
            # Check for signal changes
            if self._is_new_signal(timeframe, signal):
                logger.info(f"New {timeframe} signal: {signal['signal']} (confidence: {signal['confidence']:.3f})")
                self.last_signals[timeframe] = signal
                return signal
            
            return None
            
        except Exception as e:
            logger.error(f"Error generating signal for {timeframe}: {e}")
            return None
    
    def _get_market_context(self, data: pd.DataFrame) -> Dict:
        """Get current market context for better signal interpretation"""
        try:
            latest = data.iloc[-1]
            
            context = {
                'trend': 'bullish' if latest['close'] > latest['sma_50'] else 'bearish',
                'volatility': 'high' if latest['atr'] > latest['atr'].rolling(20).mean() else 'normal',
                'rsi_level': 'overbought' if latest['rsi'] > 70 else 'oversold' if latest['rsi'] < 30 else 'neutral',
                'volume_trend': 'increasing' if latest['volume'] > latest['volume'].rolling(10).mean() else 'decreasing',
                'support_level': float(latest['support_level']),
                'resistance_level': float(latest['resistance_level'])
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting market context: {e}")
            return {}
    
    def _is_new_signal(self, timeframe: str, signal: Dict) -> bool:
        """Check if this is a new signal worth sending"""
        try:
            last_signal = self.last_signals.get(timeframe)
            
            if last_signal is None:
                return True
            
            # Check if signal direction changed
            if last_signal['signal'] != signal['signal']:
                return True
            
            # Check if confidence significantly improved
            confidence_improvement = signal['confidence'] - last_signal['confidence']
            if confidence_improvement > 0.1:  # 10% improvement
                return True
            
            # Check time since last signal
            last_time = datetime.fromisoformat(last_signal['timestamp'])
            current_time = datetime.fromisoformat(signal['timestamp'])
            time_diff = (current_time - last_time).total_seconds() / 3600  # hours
            
            # Send new signal if enough time has passed
            min_hours = {'15m': 0.5, '1h': 2, '4h': 8}.get(timeframe, 1)
            if time_diff > min_hours:
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking signal novelty: {e}")
            return True
    
    def send_to_tradingview(self, signal: Dict) -> bool:
        """Send signal to TradingView via webhook"""
        try:
            if not settings.TRADINGVIEW_WEBHOOK_URL:
                logger.warning("TradingView webhook URL not configured")
                return False
            
            # Prepare webhook payload
            payload = {
                'secret': settings.WEBHOOK_SECRET,
                'timeframe': signal['timeframe'],
                'action': signal['signal'].lower(),
                'confidence': signal['confidence'],
                'price': signal['current_price'],
                'timestamp': signal['timestamp'],
                'context': signal.get('market_context', {})
            }
            
            # Send webhook
            response = requests.post(
                settings.TRADINGVIEW_WEBHOOK_URL,
                json=payload,
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                logger.info(f"Signal sent to TradingView successfully: {signal['signal']}")
                return True
            else:
                logger.error(f"Failed to send signal to TradingView: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending signal to TradingView: {e}")
            return False
    
    def process_all_timeframes(self):
        """Process signals for all timeframes"""
        try:
            logger.info("Processing signals for all timeframes...")
            
            signals_generated = []
            
            for timeframe in settings.TIMEFRAMES:
                signal = self.generate_signal(timeframe)
                
                if signal:
                    signals_generated.append(signal)
                    
                    # Send to TradingView
                    if self.send_to_tradingview(signal):
                        # Store in history
                        self.signal_history.append(signal)
                        
                        # Limit history size
                        if len(self.signal_history) > 1000:
                            self.signal_history = self.signal_history[-500:]
            
            if signals_generated:
                logger.info(f"Generated {len(signals_generated)} new signals")
            else:
                logger.info("No new signals generated")
                
        except Exception as e:
            logger.error(f"Error processing timeframes: {e}")
    
    def start_scheduler(self):
        """Start the signal generation scheduler"""
        try:
            logger.info("Starting signal generation scheduler...")
            
            # Schedule signal generation
            schedule.every(15).minutes.do(self.process_all_timeframes)
            
            # Schedule model retraining (daily)
            schedule.every().day.at("02:00").do(self.retrain_models)
            
            self.is_running = True
            
            while self.is_running:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
                
        except Exception as e:
            logger.error(f"Error in scheduler: {e}")
    
    def retrain_models(self):
        """Retrain models with latest data"""
        try:
            logger.info("Starting model retraining...")
            
            for timeframe in settings.TIMEFRAMES:
                logger.info(f"Retraining model for {timeframe}")
                
                # Get fresh data
                data = self.data_collector.get_processed_data(timeframe=timeframe)
                
                if not data.empty:
                    # Retrain model
                    predictor = XAUUSDPredictor(model_type=settings.MODEL_TYPE)
                    metrics = predictor.train(data)
                    
                    if metrics:
                        # Update predictor
                        self.predictors[timeframe] = predictor
                        logger.info(f"Model retrained for {timeframe}: accuracy={metrics.get('val_accuracy', 0):.3f}")
                    else:
                        logger.error(f"Failed to retrain model for {timeframe}")
                else:
                    logger.warning(f"No data available for retraining {timeframe}")
            
            logger.info("Model retraining completed")
            
        except Exception as e:
            logger.error(f"Error during model retraining: {e}")
    
    def stop(self):
        """Stop the signal generator"""
        self.is_running = False
        logger.info("Signal generator stopped")
    
    def get_status(self) -> Dict:
        """Get current status of the signal generator"""
        return {
            'is_running': self.is_running,
            'last_signals': self.last_signals,
            'signal_count': len(self.signal_history),
            'models_loaded': len([p for p in self.predictors.values() if p.is_trained]),
            'timeframes': settings.TIMEFRAMES
        }


def run_signal_generator():
    """Run the signal generator as a background service"""
    generator = SignalGenerator()
    
    # Initialize models
    if not generator.initialize_models():
        logger.error("Failed to initialize models. Exiting.")
        return
    
    # Start scheduler in a separate thread
    scheduler_thread = Thread(target=generator.start_scheduler, daemon=True)
    scheduler_thread.start()
    
    logger.info("Signal generator started successfully")
    
    try:
        # Keep the main thread alive
        while True:
            time.sleep(60)
            
    except KeyboardInterrupt:
        logger.info("Shutting down signal generator...")
        generator.stop()


if __name__ == "__main__":
    run_signal_generator()

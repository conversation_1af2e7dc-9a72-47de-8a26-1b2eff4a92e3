#!/usr/bin/env python3
"""
Quick Telegram Setup for XAUUSD AI Trading System
Simple script to quickly configure Telegram bot for testing
"""

import os
import asyncio
import aiohttp
import sys
from loguru import logger

# Setup logging
logger.remove()
logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>")

class QuickTelegramSetup:
    """Quick Telegram setup class"""
    
    def __init__(self):
        self.bot_token = None
        self.chat_id = None
    
    async def test_bot_token(self, token: str) -> tuple[bool, dict]:
        """Test if bot token is valid"""
        try:
            url = f"https://api.telegram.org/bot{token}/getMe"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('ok'):
                            return True, data.get('result', {})
                    return False, {}
                    
        except Exception as e:
            logger.error(f"Error testing bot token: {e}")
            return False, {}
    
    async def get_chat_id(self, token: str) -> str:
        """Get chat ID from recent messages"""
        try:
            url = f"https://api.telegram.org/bot{token}/getUpdates"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('ok') and data.get('result'):
                            updates = data['result']
                            if updates:
                                # Get the most recent chat ID
                                latest_update = updates[-1]
                                chat = latest_update.get('message', {}).get('chat', {})
                                return str(chat.get('id', ''))
                    return ""
                    
        except Exception as e:
            logger.error(f"Error getting chat ID: {e}")
            return ""
    
    async def send_test_message(self, token: str, chat_id: str) -> bool:
        """Send a test message"""
        try:
            url = f"https://api.telegram.org/bot{token}/sendMessage"
            
            message = """
🤖 <b>XAUUSD AI Trading System</b>

✅ <b>Quick Setup Complete!</b>

Your Telegram bot is now configured and ready for testing.

🧪 <b>Next Steps:</b>
1. Run comprehensive tests: <code>python test_telegram_comprehensive.py</code>
2. Start your trading system
3. Begin receiving live signals!

📊 <b>Test completed at:</b> {time}
""".format(time=asyncio.get_event_loop().time())
            
            payload = {
                "chat_id": chat_id,
                "text": message,
                "parse_mode": "HTML",
                "disable_web_page_preview": True
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(f"Error sending test message: {e}")
            return False
    
    def save_to_env(self, token: str, chat_id: str):
        """Save configuration to .env file"""
        try:
            env_content = []
            env_file = ".env"
            
            # Read existing .env file if it exists
            if os.path.exists(env_file):
                with open(env_file, 'r') as f:
                    env_content = f.readlines()
            
            # Remove existing Telegram configuration
            env_content = [line for line in env_content if not line.startswith('TELEGRAM_')]
            
            # Add new Telegram configuration
            env_content.append(f"\n# Telegram Configuration\n")
            env_content.append(f"TELEGRAM_BOT_TOKEN={token}\n")
            env_content.append(f"TELEGRAM_CHAT_ID={chat_id}\n")
            
            # Write back to .env file
            with open(env_file, 'w') as f:
                f.writelines(env_content)
            
            logger.info(f"✅ Configuration saved to {env_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error saving configuration: {e}")
            return False
    
    def check_existing_config(self) -> bool:
        """Check if Telegram is already configured"""
        # Check environment variables
        bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        chat_id = os.getenv('TELEGRAM_CHAT_ID')
        
        if bot_token and chat_id:
            logger.info("✅ Telegram already configured via environment variables")
            self.bot_token = bot_token
            self.chat_id = chat_id
            return True
        
        # Check .env file
        if os.path.exists('.env'):
            with open('.env', 'r') as f:
                for line in f:
                    if line.startswith('TELEGRAM_BOT_TOKEN='):
                        bot_token = line.split('=', 1)[1].strip()
                    elif line.startswith('TELEGRAM_CHAT_ID='):
                        chat_id = line.split('=', 1)[1].strip()
        
        if bot_token and chat_id:
            logger.info("✅ Telegram already configured via .env file")
            self.bot_token = bot_token
            self.chat_id = chat_id
            return True
        
        return False

async def main():
    """Main setup function"""
    print("🚀 XAUUSD AI Trading System - Quick Telegram Setup")
    print("=" * 55)
    
    setup = QuickTelegramSetup()
    
    # Check if already configured
    if setup.check_existing_config():
        print(f"\n✅ Telegram is already configured!")
        print(f"Bot Token: {setup.bot_token[:10]}...{setup.bot_token[-5:]}")
        print(f"Chat ID: {setup.chat_id}")
        
        choice = input("\nDo you want to test the existing configuration? (y/n): ").strip().lower()
        if choice in ['y', 'yes']:
            print("\n🧪 Running comprehensive test...")
            os.system("python test_telegram_comprehensive.py")
        return
    
    print("\n📋 Quick Setup Process:")
    print("1. Create a Telegram bot with @BotFather")
    print("2. Get your bot token")
    print("3. Send a message to your bot")
    print("4. We'll auto-detect your chat ID")
    
    # Get bot token
    while True:
        token = input("\n🔑 Enter your bot token: ").strip()
        if not token:
            print("❌ Bot token cannot be empty")
            continue
        
        print("🔍 Testing bot token...")
        is_valid, bot_info = await setup.test_bot_token(token)
        
        if is_valid:
            bot_name = bot_info.get('first_name', 'Unknown')
            bot_username = bot_info.get('username', 'Unknown')
            logger.info(f"✅ Bot token valid! Bot: {bot_name} (@{bot_username})")
            setup.bot_token = token
            break
        else:
            print("❌ Invalid bot token. Please check and try again.")
    
    # Get chat ID
    print(f"\n💬 Now send a message to your bot (@{bot_info.get('username', 'your_bot')})")
    input("Press Enter after sending a message to your bot...")
    
    print("🔍 Looking for your chat ID...")
    chat_id = await setup.get_chat_id(token)
    
    if chat_id:
        logger.info(f"✅ Found chat ID: {chat_id}")
        setup.chat_id = chat_id
    else:
        print("❌ Could not auto-detect chat ID")
        chat_id = input("Please enter your chat ID manually: ").strip()
        if not chat_id:
            print("❌ Cannot proceed without chat ID")
            return
        setup.chat_id = chat_id
    
    # Test setup
    print("\n🧪 Testing setup...")
    success = await setup.send_test_message(token, chat_id)
    
    if success:
        print("✅ Test message sent! Check your Telegram.")
        
        # Save configuration
        save_choice = input("\nSave configuration to .env file? (y/n): ").strip().lower()
        if save_choice in ['y', 'yes']:
            setup.save_to_env(token, chat_id)
        
        print("\n🎉 Quick Setup Complete!")
        print("\n📋 Next Steps:")
        print("1. Run comprehensive tests: python test_telegram_comprehensive.py")
        print("2. Start your AI trading system")
        print("3. Begin receiving live signals!")
        
        # Ask if user wants to run comprehensive tests
        test_choice = input("\nRun comprehensive tests now? (y/n): ").strip().lower()
        if test_choice in ['y', 'yes']:
            print("\n🧪 Running comprehensive tests...")
            os.system("python test_telegram_comprehensive.py")
    else:
        print("❌ Test message failed. Please check your configuration.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ Setup interrupted by user")
    except Exception as e:
        print(f"\n❌ Setup failed with error: {e}")
        sys.exit(1)

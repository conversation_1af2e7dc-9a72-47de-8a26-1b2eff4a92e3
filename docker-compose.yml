version: '3.8'

services:
  xauusd-ai-api:
    build: .
    container_name: xauusd-ai-trading
    ports:
      - "8000:8000"
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - LOG_LEVEL=INFO
      - MODEL_TYPE=lstm
      - SIGNAL_THRESHOLD=0.6
      - CONFIDENCE_THRESHOLD=0.7
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
      - ./data:/app/data
      - ./.env:/app/.env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Redis for caching (uncomment if needed)
  # redis:
  #   image: redis:7-alpine
  #   container_name: xauusd-redis
  #   ports:
  #     - "6379:6379"
  #   restart: unless-stopped

  # Optional: PostgreSQL for data storage (uncomment if needed)
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: xauusd-postgres
  #   environment:
  #     POSTGRES_DB: xauusd_trading
  #     POSTGRES_USER: xauusd_user
  #     POSTGRES_PASSWORD: your_secure_password
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   restart: unless-stopped

  # Optional: Monitoring with Prometheus (uncomment if needed)
  # prometheus:
  #   image: prom/prometheus:latest
  #   container_name: xauusd-prometheus
  #   ports:
  #     - "9090:9090"
  #   volumes:
  #     - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
  #   restart: unless-stopped

  # Optional: Grafana for visualization (uncomment if needed)
  # grafana:
  #   image: grafana/grafana:latest
  #   container_name: xauusd-grafana
  #   ports:
  #     - "3000:3000"
  #   environment:
  #     - GF_SECURITY_ADMIN_PASSWORD=admin
  #   volumes:
  #     - grafana_data:/var/lib/grafana
  #   restart: unless-stopped

volumes:
  postgres_data:
  grafana_data:

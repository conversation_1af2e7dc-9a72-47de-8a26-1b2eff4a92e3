"""
XAUUSD AI Trading System - Enhanced API with Telegram Integration
Production-ready API with TradingView webhook support and Telegram notifications.
"""
from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional
import asyncio
import json
import os
from datetime import datetime
from loguru import logger
import uvicorn

# Import our telegram service
from telegram_service_production import initialize_telegram, send_signal_notification, send_alert_notification, test_telegram_connection

app = FastAPI(title="XAUUSD AI Trading API", version="2.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN", "")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID", "")

# Initialize Telegram service
if TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID:
    telegram_service = initialize_telegram(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID)
    logger.info("✅ Telegram service initialized")
else:
    logger.warning("⚠️ Telegram credentials not found in environment variables")

# Pydantic models
class TradingViewWebhook(BaseModel):
    """TradingView webhook data model"""
    signal: str  # BUY, SELL, HOLD
    price: float
    timeframe: str
    timestamp: str
    indicator_data: Optional[Dict[str, Any]] = {}
    confidence: Optional[float] = 0.5

class AISignal(BaseModel):
    """AI-generated trading signal"""
    signal: str
    confidence: float
    current_price: float
    timeframe: str
    indicators: Dict[str, Any]
    timestamp: str
    reasoning: Optional[str] = ""

class SystemAlert(BaseModel):
    """System alert model"""
    alert_type: str
    message: str
    severity: str = "INFO"

# Global state
system_stats = {
    "signals_today": 0,
    "last_signal": None,
    "api_calls": 0,
    "start_time": datetime.now().isoformat(),
    "telegram_enabled": bool(TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID)
}

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "🏆 XAUUSD AI Trading System API",
        "version": "2.0.0",
        "status": "operational",
        "telegram_enabled": system_stats["telegram_enabled"],
        "documentation": "/docs"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "telegram_enabled": system_stats["telegram_enabled"]
    }

@app.get("/status")
async def get_status():
    """Detailed system status"""
    return {
        "system": "XAUUSD AI Trading System",
        "version": "2.0.0",
        "status": "operational",
        "uptime": datetime.now().isoformat(),
        "stats": system_stats,
        "telegram": {
            "enabled": system_stats["telegram_enabled"],
            "bot_token_set": bool(TELEGRAM_BOT_TOKEN),
            "chat_id_set": bool(TELEGRAM_CHAT_ID)
        }
    }

@app.post("/webhook/tradingview")
async def tradingview_webhook(webhook_data: TradingViewWebhook, background_tasks: BackgroundTasks):
    """
    TradingView webhook endpoint
    This receives signals from TradingView Pine Script indicators
    """
    try:
        logger.info(f"📈 Received TradingView webhook: {webhook_data.signal}")
        
        # Process the signal
        signal_data = {
            "signal": webhook_data.signal,
            "confidence": webhook_data.confidence,
            "current_price": webhook_data.price,
            "timeframe": webhook_data.timeframe,
            "timestamp": webhook_data.timestamp,
            "indicators": webhook_data.indicator_data,
            "source": "TradingView"
        }
        
        # Update stats
        system_stats["signals_today"] += 1
        system_stats["last_signal"] = signal_data
        system_stats["api_calls"] += 1
        
        # Send Telegram notification in background
        if system_stats["telegram_enabled"]:
            background_tasks.add_task(send_signal_notification, signal_data)
        
        logger.info(f"✅ TradingView signal processed: {webhook_data.signal}")
        
        return {
            "status": "success",
            "message": "Signal received and processed",
            "signal": webhook_data.signal,
            "telegram_sent": system_stats["telegram_enabled"],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Error processing TradingView webhook: {e}")
        
        # Send error alert
        if system_stats["telegram_enabled"]:
            background_tasks.add_task(
                send_alert_notification,
                "TradingView Webhook Error",
                f"Failed to process webhook: {str(e)}",
                "ERROR"
            )
        
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/signal/ai")
async def ai_signal(signal_data: AISignal, background_tasks: BackgroundTasks):
    """
    AI-generated signal endpoint
    For internal AI model predictions
    """
    try:
        logger.info(f"🤖 Received AI signal: {signal_data.signal}")
        
        # Convert to dict for telegram
        signal_dict = {
            "signal": signal_data.signal,
            "confidence": signal_data.confidence,
            "current_price": signal_data.current_price,
            "timeframe": signal_data.timeframe,
            "timestamp": signal_data.timestamp,
            "indicators": signal_data.indicators,
            "reasoning": signal_data.reasoning,
            "source": "AI Model"
        }
        
        # Update stats
        system_stats["signals_today"] += 1
        system_stats["last_signal"] = signal_dict
        system_stats["api_calls"] += 1
        
        # Send Telegram notification
        if system_stats["telegram_enabled"]:
            background_tasks.add_task(send_signal_notification, signal_dict)
        
        logger.info(f"✅ AI signal processed: {signal_data.signal}")
        
        return {
            "status": "success",
            "message": "AI signal processed",
            "signal": signal_data.signal,
            "confidence": signal_data.confidence,
            "telegram_sent": system_stats["telegram_enabled"],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Error processing AI signal: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/alert")
async def send_alert(alert_data: SystemAlert, background_tasks: BackgroundTasks):
    """Send system alert"""
    try:
        if system_stats["telegram_enabled"]:
            background_tasks.add_task(
                send_alert_notification,
                alert_data.alert_type,
                alert_data.message,
                alert_data.severity
            )
        
        return {
            "status": "success",
            "message": "Alert sent",
            "telegram_sent": system_stats["telegram_enabled"]
        }
        
    except Exception as e:
        logger.error(f"❌ Error sending alert: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/telegram/test")
async def test_telegram():
    """Test Telegram connection"""
    if not system_stats["telegram_enabled"]:
        raise HTTPException(status_code=400, detail="Telegram not configured")
    
    try:
        result = await test_telegram_connection()
        return {
            "status": "success" if result else "failed",
            "message": "Telegram test completed",
            "result": result
        }
    except Exception as e:
        logger.error(f"❌ Telegram test error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/signals/recent")
async def get_recent_signals():
    """Get recent signals"""
    return {
        "last_signal": system_stats["last_signal"],
        "signals_today": system_stats["signals_today"],
        "timestamp": datetime.now().isoformat()
    }

@app.post("/config/telegram")
async def configure_telegram(bot_token: str, chat_id: str):
    """Configure Telegram credentials (for testing)"""
    try:
        global telegram_service
        telegram_service = initialize_telegram(bot_token, chat_id)
        system_stats["telegram_enabled"] = True
        
        # Test connection
        test_result = await test_telegram_connection()
        
        return {
            "status": "success",
            "message": "Telegram configured",
            "test_result": test_result
        }
        
    except Exception as e:
        logger.error(f"❌ Error configuring Telegram: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    logger.info("🚀 Starting XAUUSD AI Trading API with Telegram integration...")
    uvicorn.run(
        "enhanced_api_with_telegram:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )

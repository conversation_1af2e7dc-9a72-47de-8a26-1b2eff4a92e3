"""
Quick Start Script for XAUUSD AI Trading System
"""
import subprocess
import sys
import time
import requests
from datetime import datetime
from loguru import logger


def setup_logging():
    """Setup logging"""
    logger.remove()
    logger.add(
        sys.stdout,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )


def check_api_health():
    """Check if API is running and healthy"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            return True, data
        return False, None
    except:
        return False, None


def start_api_server():
    """Start the API server"""
    logger.info("🚀 Starting XAUUSD AI Trading API Server...")
    
    try:
        # Start the API server in background
        process = subprocess.Popen(
            [sys.executable, "api_server.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait for server to start
        logger.info("⏳ Waiting for server to start...")
        for i in range(30):  # Wait up to 30 seconds
            time.sleep(1)
            is_healthy, health_data = check_api_health()
            if is_healthy:
                logger.info("✅ API Server started successfully!")
                logger.info(f"📊 Models loaded: {health_data.get('models_loaded', 0)}")
                return process
            
        logger.error("❌ API Server failed to start within 30 seconds")
        process.terminate()
        return None
        
    except Exception as e:
        logger.error(f"❌ Failed to start API server: {e}")
        return None


def test_system():
    """Test the system functionality"""
    logger.info("🧪 Testing system functionality...")
    
    try:
        # Test health
        is_healthy, health_data = check_api_health()
        if not is_healthy:
            logger.error("❌ Health check failed")
            return False
        
        logger.info(f"✅ Health check passed - Models: {health_data.get('models_loaded', 0)}")
        
        # Test signal generation
        response = requests.post(
            "http://localhost:8000/signal",
            json={"timeframe": "1h", "force_generate": True},
            timeout=30
        )
        
        if response.status_code == 200:
            signal = response.json()
            logger.info("✅ Signal generation working!")
            logger.info(f"📈 Latest signal: {signal.get('signal', 'N/A')} "
                       f"(confidence: {signal.get('confidence', 0):.1%})")
        else:
            logger.warning(f"⚠️ Signal generation returned: {response.status_code}")
        
        # Test market data
        response = requests.get("http://localhost:8000/data/1h?limit=1", timeout=10)
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ Market data access working - Latest price: ${data.get('latest_price', 0):.2f}")
        else:
            logger.warning(f"⚠️ Market data access returned: {response.status_code}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ System test failed: {e}")
        return False


def show_status():
    """Show current system status"""
    logger.info("📊 System Status:")
    logger.info("=" * 50)
    
    try:
        # Get status
        response = requests.get("http://localhost:8000/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            logger.info(f"🔄 Running: {status.get('is_running', False)}")
            logger.info(f"🤖 Models Loaded: {status.get('models_loaded', 0)}")
            logger.info(f"📈 Timeframes: {', '.join(status.get('timeframes', []))}")
            logger.info(f"📊 Total Signals: {status.get('signal_count', 0)}")
            
            # Show last signals
            last_signals = status.get('last_signals', {})
            if last_signals:
                logger.info("🎯 Last Signals:")
                for tf, signal in last_signals.items():
                    if signal:
                        logger.info(f"   {tf}: {signal.get('signal', 'N/A')} "
                                   f"({signal.get('confidence', 0):.1%})")
        else:
            logger.error("❌ Could not get system status")
            
    except Exception as e:
        logger.error(f"❌ Status check failed: {e}")


def main():
    """Main function"""
    setup_logging()
    
    logger.info("🎯 XAUUSD AI Trading System - Quick Start")
    logger.info("=" * 50)
    
    # Check if already running
    is_healthy, health_data = check_api_health()
    if is_healthy:
        logger.info("✅ System is already running!")
        show_status()
        
        # Test functionality
        test_system()
        
        logger.info("\n🌐 Access Points:")
        logger.info("   API Server: http://localhost:8000")
        logger.info("   Health Check: http://localhost:8000/health")
        logger.info("   API Docs: http://localhost:8000/docs")
        logger.info("   Status: http://localhost:8000/status")
        
        return
    
    # Start the system
    logger.info("🚀 Starting XAUUSD AI Trading System...")
    
    process = start_api_server()
    if not process:
        logger.error("❌ Failed to start system")
        return
    
    # Test the system
    if test_system():
        logger.info("✅ System started successfully!")
        show_status()
        
        logger.info("\n🌐 Access Points:")
        logger.info("   API Server: http://localhost:8000")
        logger.info("   Health Check: http://localhost:8000/health")
        logger.info("   API Docs: http://localhost:8000/docs")
        logger.info("   Status: http://localhost:8000/status")
        
        logger.info("\n📋 Next Steps:")
        logger.info("   1. Open TradingView and add the Pine Script indicator")
        logger.info("   2. Configure webhook URLs in .env file")
        logger.info("   3. Set up alerts in TradingView")
        logger.info("   4. Monitor signals at http://localhost:8000/status")
        
        logger.info("\n🎯 System is ready for trading!")
        
        # Keep running
        try:
            logger.info("\n⏳ System running... Press Ctrl+C to stop")
            while True:
                time.sleep(60)
                # Periodic health check
                is_healthy, _ = check_api_health()
                if not is_healthy:
                    logger.error("❌ System health check failed!")
                    break
                    
        except KeyboardInterrupt:
            logger.info("\n🛑 Stopping system...")
            process.terminate()
            logger.info("✅ System stopped")
    else:
        logger.error("❌ System tests failed")
        process.terminate()


if __name__ == "__main__":
    main()

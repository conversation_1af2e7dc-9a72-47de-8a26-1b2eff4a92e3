# TradingView Integration Guide

This guide explains how to integrate the XAUUSD AI Trading System with TradingView for real-time signal display and automated trading.

## 📋 Table of Contents

- [Overview](#overview)
- [Pine Script Setup](#pine-script-setup)
- [Webhook Configuration](#webhook-configuration)
- [Alert Setup](#alert-setup)
- [Testing Integration](#testing-integration)
- [Troubleshooting](#troubleshooting)

## 🔍 Overview

The integration works through a bidirectional webhook system:

1. **TradingView → AI System**: Market data and alerts sent to AI system
2. **AI System → TradingView**: Trading signals sent back to TradingView
3. **Pine Script Indicator**: Displays AI signals on TradingView charts

## 📊 Pine Script Setup

### 1. Add the Indicator

1. **Open TradingView** and navigate to the Pine Editor
2. **Create new indicator** and copy the content from `xauusd_ai_indicator.pine`
3. **Save the script** with name "XAUUSD AI Trading Signals"
4. **Add to chart** by clicking "Add to Chart"

### 2. Configure Indicator Settings

The indicator includes several configuration sections:

#### AI Configuration
```pinescript
ai_enabled = true                    // Enable AI signals
signal_source = "External API"      // Signal source type
api_url = "http://localhost:8000"   // Your API URL
webhook_secret = "your-secret-key"  // Webhook secret
```

#### Timeframes
```pinescript
timeframe_15m = true    // Enable 15-minute signals
timeframe_1h = true     // Enable 1-hour signals  
timeframe_4h = true     // Enable 4-hour signals
```

#### Signal Display
```pinescript
show_buy_signals = true      // Show buy signals
show_sell_signals = true     // Show sell signals
show_confidence = true       // Show confidence levels
show_price_levels = true     // Show price level lines
```

#### Signal Filtering
```pinescript
min_confidence = 0.6         // Minimum confidence threshold
max_signals_per_day = 10     // Maximum signals per day
```

### 3. Visual Elements

The indicator displays:

- **Buy Signals**: Green arrows below candles with "BUY AI" label
- **Sell Signals**: Red arrows above candles with "SELL AI" label
- **Confidence Levels**: Percentage confidence displayed near signals
- **Price Levels**: Dashed lines showing signal price levels
- **Information Table**: Real-time status and statistics
- **Technical Context**: Moving averages and Bollinger Bands

## 🔗 Webhook Configuration

### 1. Set Up Your API Endpoint

Ensure your AI system is running and accessible:

```bash
# Local development
python api_server.py

# Docker deployment
docker-compose up -d

# Check API health
curl http://localhost:8000/health
```

### 2. Configure Webhook URLs

#### For TradingView → AI System
Set up webhook endpoint in your AI system:
```
POST http://your-domain.com:8000/webhook/tradingview
```

#### For AI System → TradingView
Configure TradingView webhook URL in your `.env`:
```env
TRADINGVIEW_WEBHOOK_URL=https://your-tradingview-webhook-url.com/webhook
WEBHOOK_SECRET=your-secure-secret-key
```

### 3. Webhook Payload Format

#### TradingView to AI System
```json
{
    "symbol": "XAUUSD",
    "timeframe": "1h",
    "timestamp": "2024-01-15T10:30:00Z",
    "open": 2040.50,
    "high": 2045.80,
    "low": 2038.20,
    "close": 2043.75,
    "volume": 1500,
    "indicators": {
        "rsi": 65.5,
        "macd": 0.25,
        "bb_upper": 2050.0,
        "bb_lower": 2035.0
    },
    "alert_message": "XAUUSD price alert",
    "secret": "your-secret-key"
}
```

#### AI System to TradingView
```json
{
    "timestamp": "2024-01-15T10:30:00Z",
    "symbol": "XAUUSD",
    "timeframe": "1h",
    "action": "buy",
    "confidence": 0.75,
    "price": 2043.75,
    "message": "🤖 XAUUSD AI BUY SIGNAL\n📊 Confidence: 75%",
    "secret": "your-secret-key"
}
```

## 🚨 Alert Setup

### 1. Create Market Data Alerts

Set up alerts in TradingView to send market data to your AI system:

1. **Right-click on chart** → "Add Alert"
2. **Configure alert conditions**:
   - Condition: "Any alert() function call"
   - Options: "Once Per Bar Close"
3. **Set webhook URL**: `http://your-domain.com:8000/webhook/tradingview`
4. **Configure message**:
```json
{
    "symbol": "{{ticker}}",
    "timeframe": "{{interval}}",
    "timestamp": "{{time}}",
    "open": {{open}},
    "high": {{high}},
    "low": {{low}},
    "close": {{close}},
    "volume": {{volume}},
    "secret": "your-secret-key"
}
```

### 2. Create Signal Reception Alerts

Set up alerts to receive AI-generated signals:

1. **Create new alert** on your Pine Script indicator
2. **Select condition**: "XAUUSD AI Trading Signals"
3. **Choose alert type**: "AI Buy Signal" or "AI Sell Signal"
4. **Configure notifications**:
   - Email notifications
   - Mobile push notifications
   - Webhook to trading platform (optional)

### 3. Alert Message Templates

#### Buy Signal Alert
```
🤖 XAUUSD AI BUY SIGNAL
📊 Timeframe: {{timeframe}}
💰 Price: ${{close}}
🎯 Confidence: {{confidence}}%
📈 Trend: {{trend}}
⏰ Time: {{time}}
```

#### Sell Signal Alert
```
🤖 XAUUSD AI SELL SIGNAL
📊 Timeframe: {{timeframe}}
💰 Price: ${{close}}
🎯 Confidence: {{confidence}}%
📉 Trend: {{trend}}
⏰ Time: {{time}}
```

## 🧪 Testing Integration

### 1. Test Webhook Reception

Test if your AI system receives TradingView webhooks:

```bash
# Send test webhook
curl -X POST http://localhost:8000/webhook/tradingview \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "XAUUSD",
    "timeframe": "1h",
    "close": 2043.75,
    "volume": 1000,
    "secret": "your-secret-key"
  }'
```

### 2. Test Signal Generation

Test AI signal generation:

```bash
# Generate test signal
curl -X POST http://localhost:8000/signal \
  -H "Content-Type: application/json" \
  -d '{"timeframe": "1h", "force_generate": true}'
```

### 3. Test Pine Script Display

1. **Add indicator to chart**
2. **Check for visual elements**:
   - Moving averages displayed
   - Information table visible
   - No error messages
3. **Verify settings**:
   - All timeframes enabled
   - Confidence threshold set correctly
   - Colors and styles as expected

### 4. End-to-End Test

Complete integration test:

1. **Trigger TradingView alert** (manually or wait for market condition)
2. **Check AI system logs** for webhook reception
3. **Verify signal generation** in API logs
4. **Confirm signal display** on TradingView chart
5. **Check alert notifications** (email, mobile, etc.)

## 🔧 Troubleshooting

### Common Issues

#### 1. Webhooks Not Received

**Symptoms**: No data in AI system logs
**Solutions**:
- Check firewall settings
- Verify webhook URL is accessible
- Test with curl or Postman
- Check TradingView alert configuration

#### 2. Signals Not Displayed

**Symptoms**: Pine Script shows no signals
**Solutions**:
- Verify AI system is generating signals
- Check Pine Script indicator settings
- Ensure minimum confidence threshold is met
- Review signal filtering parameters

#### 3. Authentication Errors

**Symptoms**: HTTP 401 errors in logs
**Solutions**:
- Verify webhook secret matches in both systems
- Check .env configuration
- Ensure secret is included in webhook payload

#### 4. Performance Issues

**Symptoms**: Slow signal generation or display
**Solutions**:
- Optimize AI model parameters
- Reduce data collection frequency
- Use caching for repeated requests
- Consider upgrading server resources

### Debug Mode

Enable debug logging for troubleshooting:

```env
LOG_LEVEL=DEBUG
```

Check logs for detailed information:
```bash
# View real-time logs
tail -f logs/xauusd_ai.log

# Search for specific issues
grep "ERROR" logs/xauusd_ai.log
grep "webhook" logs/xauusd_ai.log
```

### Network Configuration

#### Firewall Settings
Ensure these ports are open:
- **8000**: API server
- **443/80**: HTTPS/HTTP for webhooks

#### Reverse Proxy (Nginx)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location /webhook/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### TradingView Limitations

Be aware of TradingView limitations:

1. **Webhook Rate Limits**: Maximum 1 webhook per second
2. **Alert Limits**: Varies by subscription plan
3. **Pine Script Limitations**: Cannot make HTTP requests directly
4. **Data Delays**: Real-time data may have slight delays

## 📈 Advanced Configuration

### Multi-Symbol Support

To support multiple symbols, modify the webhook handler:

```python
# In webhook_handler.py
SUPPORTED_SYMBOLS = ['XAUUSD', 'EURUSD', 'GBPUSD']

def validate_symbol(symbol):
    return symbol in SUPPORTED_SYMBOLS
```

### Custom Indicators

Add custom technical indicators to the Pine Script:

```pinescript
// Custom indicator example
custom_ma = ta.sma(close, input.int(21, "Custom MA Period"))
plot(custom_ma, "Custom MA", color=color.yellow)
```

### Risk Management

Implement risk management in Pine Script:

```pinescript
// Risk management parameters
risk_per_trade = input.float(2.0, "Risk Per Trade (%)", minval=0.1, maxval=10.0)
max_drawdown = input.float(10.0, "Max Drawdown (%)", minval=1.0, maxval=50.0)

// Calculate position size based on risk
atr_value = ta.atr(14)
stop_loss_distance = atr_value * 2
position_size = (account_balance * risk_per_trade / 100) / stop_loss_distance
```

## 📞 Support

For integration support:

1. **Check logs** for error messages
2. **Review documentation** for configuration details
3. **Test components** individually before full integration
4. **Use debug mode** for detailed troubleshooting
5. **Create GitHub issues** for bugs or feature requests

## 🔄 Updates

Keep your integration updated:

1. **Monitor API changes** in the AI system
2. **Update Pine Script** when new features are added
3. **Test thoroughly** after any updates
4. **Backup configurations** before making changes

---

**Note**: This integration requires a TradingView account with webhook capabilities (Pro plan or higher) and a publicly accessible server for the AI system.

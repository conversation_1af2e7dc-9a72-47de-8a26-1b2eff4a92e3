"""
WSGI Application Entry Point for XAUUSD AI Trading System
This file is used by Gun<PERSON> to serve the FastAPI application in production.
"""
import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Set environment variables if needed
os.environ.setdefault("PYTHONPATH", str(current_dir))

try:
    from api_server import app
    
    # This is the WSGI application that Gun<PERSON> will use
    application = app
    
    print("✅ WSGI application loaded successfully")
    
except ImportError as e:
    print(f"❌ Failed to import FastAPI app: {e}")
    raise
except Exception as e:
    print(f"❌ Error loading WSGI application: {e}")
    raise

if __name__ == "__main__":
    # For testing the WSGI app directly
    import uvicorn
    uvicorn.run(application, host="0.0.0.0", port=8000)

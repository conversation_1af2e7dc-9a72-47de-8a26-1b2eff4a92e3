#!/usr/bin/env python3
"""
XAUUSD AI System - Connection Test Script
Tests all API endpoints and connections
"""

import requests
import json
import sys
from datetime import datetime
from typing import Dict, List, Tuple

# Test configuration
ENDPOINTS = {
    "Local Health": "http://localhost:8000/health",
    "Local API Health": "http://localhost:8000/api/health",
    "Local Status": "http://localhost:8000/status",
    "Local API Status": "http://localhost:8000/api/status",
    "Local Dashboard": "http://localhost:3000",
    "Direct IP Health": "http://************:8000/health",
    "Direct IP API Health": "http://************:8000/api/health",
    "Domain API Health": "https://greateatfood.com/api/health",
    "Domain Dashboard": "https://greateatfood.com/dashboard/",
}

WEBHOOK_ENDPOINTS = {
    "Local Webhook": "http://localhost:8000/webhook/tradingview",
    "Local API Webhook": "http://localhost:8000/api/webhook/tradingview",
    "Direct IP Webhook": "http://************:8000/webhook/tradingview",
    "Domain API Webhook": "https://greateatfood.com/api/webhook/tradingview",
}

# Test webhook payload
TEST_WEBHOOK_PAYLOAD = {
    "signal": "BUY",
    "price": 2650.50,
    "timeframe": "1h",
    "timestamp": "2025-01-13T19:15:07.760808",
    "confidence": 0.85,
    "indicator_data": {
        "rsi": 65,
        "macd_signal": "bullish"
    }
}

def print_header(title: str):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

def print_result(name: str, success: bool, details: str = ""):
    """Print test result with formatting"""
    status = "✅ SUCCESS" if success else "❌ FAILED"
    print(f"{status:12} - {name:25} {details}")

def test_endpoint(name: str, url: str, timeout: int = 10) -> Tuple[bool, str]:
    """Test a single endpoint"""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            return True, f"Status: {response.status_code}"
        else:
            return False, f"Status: {response.status_code}"
    except requests.exceptions.ConnectionError:
        return False, "Connection refused"
    except requests.exceptions.Timeout:
        return False, "Timeout"
    except requests.exceptions.RequestException as e:
        return False, f"Error: {str(e)[:50]}"

def test_webhook(name: str, url: str, payload: Dict, timeout: int = 10) -> Tuple[bool, str]:
    """Test a webhook endpoint"""
    try:
        response = requests.post(
            url, 
            json=payload, 
            timeout=timeout,
            headers={'Content-Type': 'application/json'}
        )
        if response.status_code in [200, 201]:
            return True, f"Status: {response.status_code}"
        elif response.status_code == 401:
            return False, f"Status: {response.status_code} (Auth required)"
        elif response.status_code == 404:
            return False, f"Status: {response.status_code} (Not found)"
        else:
            return False, f"Status: {response.status_code}"
    except requests.exceptions.ConnectionError:
        return False, "Connection refused"
    except requests.exceptions.Timeout:
        return False, "Timeout"
    except requests.exceptions.RequestException as e:
        return False, f"Error: {str(e)[:50]}"

def main():
    """Run all connection tests"""
    print("🚀 XAUUSD AI Trading System - Connection Test")
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test regular endpoints
    print_header("Testing API Endpoints")
    
    success_count = 0
    total_count = len(ENDPOINTS)
    
    for name, url in ENDPOINTS.items():
        success, details = test_endpoint(name, url)
        print_result(name, success, details)
        if success:
            success_count += 1
    
    # Test webhook endpoints
    print_header("Testing Webhook Endpoints")
    
    webhook_success_count = 0
    webhook_total_count = len(WEBHOOK_ENDPOINTS)
    
    for name, url in WEBHOOK_ENDPOINTS.items():
        success, details = test_webhook(name, url, TEST_WEBHOOK_PAYLOAD)
        print_result(name, success, details)
        if success:
            webhook_success_count += 1
    
    # Summary
    print_header("Test Summary")
    
    total_success = success_count + webhook_success_count
    total_tests = total_count + webhook_total_count
    
    print(f"📊 API Endpoints:     {success_count}/{total_count} passed")
    print(f"📊 Webhook Endpoints: {webhook_success_count}/{webhook_total_count} passed")
    print(f"📊 Overall:           {total_success}/{total_tests} passed")
    
    success_rate = (total_success / total_tests) * 100
    print(f"📊 Success Rate:      {success_rate:.1f}%")
    
    # Recommendations
    print_header("Recommendations")
    
    if success_count < total_count:
        print("🔧 API Issues Found:")
        print("   - Check if API server is running: ps aux | grep api_server")
        print("   - Check API logs: tail -f logs/api_server.log")
        print("   - Restart API server: ./deploy_production.sh")
    
    if webhook_success_count < webhook_total_count:
        print("🔧 Webhook Issues Found:")
        print("   - Check Nginx configuration: sudo nginx -t")
        print("   - Check Nginx logs: sudo tail -f /var/log/nginx/error.log")
        print("   - Restart Nginx: sudo systemctl restart nginx")
    
    if "Domain" in str([name for name, url in ENDPOINTS.items() if not test_endpoint(name, url)[0]]):
        print("🔧 Domain Issues Found:")
        print("   - Check DNS resolution: nslookup greateatfood.com")
        print("   - Check SSL certificate: curl -I https://greateatfood.com")
        print("   - Check firewall: sudo ufw status")
    
    print(f"\n⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Exit with appropriate code
    if total_success == total_tests:
        print("🎉 All tests passed!")
        sys.exit(0)
    else:
        print("⚠️  Some tests failed. Check the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
